[2025-07-30 10:34:23] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1055 'trust_auctioneers_v2.users.name' isn't in GROUP BY (SQL: select count(*) as aggregate from (select `users`.*, MAX(ch_messages.created_at) max_created_at from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where (`ch_messages`.`from_id` = 1 or `ch_messages`.`to_id` = 1) and `users`.`id` != 1 group by `users`.`id`) as `aggregate_table`) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1055 'trust_auctioneers_v2.users.name' isn't in GROUP BY (SQL: select count(*) as aggregate from (select `users`.*, MAX(ch_messages.created_at) max_created_at from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where (`ch_messages`.`from_id` = 1 or `ch_messages`.`to_id` = 1) and `users`.`id` != 1 group by `users`.`id`) as `aggregate_table`) at C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(405): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2705): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2693): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2836): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2803): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#8 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(891): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#9 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\munafio\\chatify\\src\\Http\\Controllers\\MessagesController.php(236): Illuminate\\Database\\Eloquent\\Builder->paginate(30)
#10 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Chatify\\Http\\Controllers\\MessagesController->getContacts(Object(Illuminate\\Http\\Request))
#11 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getContacts', Array)
#12 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Chatify\\Http\\Controllers\\MessagesController), 'getContacts')
#13 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#15 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(797): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\xampp\\\\htdocs...')
#59 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1055 'trust_auctioneers_v2.users.name' isn't in GROUP BY at C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare('select count(*)...')
#1 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(405): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2705): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2693): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2836): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2803): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#10 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(891): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#11 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\munafio\\chatify\\src\\Http\\Controllers\\MessagesController.php(236): Illuminate\\Database\\Eloquent\\Builder->paginate(30)
#12 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Chatify\\Http\\Controllers\\MessagesController->getContacts(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getContacts', Array)
#14 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Chatify\\Http\\Controllers\\MessagesController), 'getContacts')
#15 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#17 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(797): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\xampp\\\\htdocs...')
#61 {main}
"} 
[2025-07-30 16:35:26] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (SQL: select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (SQL: select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1) at C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(405): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2705): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2693): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(59): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(159): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#11 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\SessionGuard->user()
#12 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(340): Illuminate\\Auth\\SessionGuard->check()
#13 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\app\\Traits\\CreatedUpdatedTrait.php(16): Illuminate\\Auth\\AuthManager->__call('check', Array)
#14 [internal function]: App\\Models\\User::bootCreatedUpdatedTrait()
#15 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(300): forward_static_call(Array)
#16 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(280): Illuminate\\Database\\Eloquent\\Model::bootTraits()
#17 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\app\\Models\\User.php(150): Illuminate\\Database\\Eloquent\\Model::boot()
#18 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(256): App\\Models\\User::boot()
#19 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(234): Illuminate\\Database\\Eloquent\\Model->bootIfNotBooted()
#20 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(184): Illuminate\\Database\\Eloquent\\Model->__construct()
#21 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(55): Illuminate\\Auth\\EloquentUserProvider->createModel()
#22 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(159): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#23 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\SessionGuard->user()
#24 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\SessionGuard->check()
#25 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#26 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(797): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\xampp\\\\htdocs...')
#66 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#6 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1217): Illuminate\\Database\\Connection->getPdo()
#7 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(486): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(405): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2705): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2693): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(59): Illuminate\\Database\\Eloquent\\Builder->first()
#20 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(159): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#21 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\SessionGuard->user()
#22 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(340): Illuminate\\Auth\\SessionGuard->check()
#23 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\app\\Traits\\CreatedUpdatedTrait.php(16): Illuminate\\Auth\\AuthManager->__call('check', Array)
#24 [internal function]: App\\Models\\User::bootCreatedUpdatedTrait()
#25 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(300): forward_static_call(Array)
#26 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(280): Illuminate\\Database\\Eloquent\\Model::bootTraits()
#27 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\app\\Models\\User.php(150): Illuminate\\Database\\Eloquent\\Model::boot()
#28 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(256): App\\Models\\User::boot()
#29 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(234): Illuminate\\Database\\Eloquent\\Model->bootIfNotBooted()
#30 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(184): Illuminate\\Database\\Eloquent\\Model->__construct()
#31 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(55): Illuminate\\Auth\\EloquentUserProvider->createModel()
#32 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(159): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#33 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\SessionGuard->user()
#34 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\SessionGuard->check()
#35 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#36 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(797): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#73 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#74 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#75 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\xampp\\\\htdocs...')
#76 {main}
"} 
[2025-07-30 16:39:08] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (SQL: select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (SQL: select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1) at C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(405): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2705): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2693): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(59): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(159): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#11 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\SessionGuard->user()
#12 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(340): Illuminate\\Auth\\SessionGuard->check()
#13 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\app\\Traits\\CreatedUpdatedTrait.php(16): Illuminate\\Auth\\AuthManager->__call('check', Array)
#14 [internal function]: App\\Models\\User::bootCreatedUpdatedTrait()
#15 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(300): forward_static_call(Array)
#16 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(280): Illuminate\\Database\\Eloquent\\Model::bootTraits()
#17 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\app\\Models\\User.php(150): Illuminate\\Database\\Eloquent\\Model::boot()
#18 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(256): App\\Models\\User::boot()
#19 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(234): Illuminate\\Database\\Eloquent\\Model->bootIfNotBooted()
#20 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(184): Illuminate\\Database\\Eloquent\\Model->__construct()
#21 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(55): Illuminate\\Auth\\EloquentUserProvider->createModel()
#22 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(159): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#23 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\SessionGuard->user()
#24 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\SessionGuard->check()
#25 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#26 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(797): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\xampp\\\\htdocs...')
#66 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#6 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1217): Illuminate\\Database\\Connection->getPdo()
#7 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(486): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(405): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2705): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2693): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(59): Illuminate\\Database\\Eloquent\\Builder->first()
#20 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(159): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#21 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\SessionGuard->user()
#22 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(340): Illuminate\\Auth\\SessionGuard->check()
#23 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\app\\Traits\\CreatedUpdatedTrait.php(16): Illuminate\\Auth\\AuthManager->__call('check', Array)
#24 [internal function]: App\\Models\\User::bootCreatedUpdatedTrait()
#25 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(300): forward_static_call(Array)
#26 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(280): Illuminate\\Database\\Eloquent\\Model::bootTraits()
#27 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\app\\Models\\User.php(150): Illuminate\\Database\\Eloquent\\Model::boot()
#28 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(256): App\\Models\\User::boot()
#29 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(234): Illuminate\\Database\\Eloquent\\Model->bootIfNotBooted()
#30 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(184): Illuminate\\Database\\Eloquent\\Model->__construct()
#31 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(55): Illuminate\\Auth\\EloquentUserProvider->createModel()
#32 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(159): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#33 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\SessionGuard->user()
#34 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\SessionGuard->check()
#35 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#36 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(797): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#73 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#74 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#75 C:\\xampp\\htdocs\\vtigo\\alt\\vertigo-ams\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\xampp\\\\htdocs...')
#76 {main}
"} 
