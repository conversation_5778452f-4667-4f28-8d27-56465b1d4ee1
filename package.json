{"private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@vueuse/core": "^10.5.0", "axios": "^1.6.0", "chart.js": "^4.4.0", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-chartjs": "^5.2.0", "vue-good-table-next": "^0.2.2", "vue-router": "^4.5.1", "vuelidate": "^0.7.7"}, "devDependencies": {"@tailwindcss/forms": "^0.5.6", "@types/lodash-es": "^4.17.12", "@types/node": "^20.8.10", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-vue": "^4.4.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "jsdom": "^26.1.0", "laravel-vite-plugin": "^0.8.1", "postcss": "^8.4.31", "prettier": "^3.0.3", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^3.2.4", "vue-tsc": "^1.8.22"}}