import{o as c,f as p,g as e,h as pe,r as P,i as q,n as J,j as z,F as me,k as fe,l as rt,t as S,m as ce,u as h,p as ze,q as ot,s as at,w as Me,v as D,x as H,T as Mt,y as br,z as Is,A as xr,B as Ce,C as he,D as Pe,E as At,G as Ye,H as W,I as ls,J as wr,K as tt,L as Te,M as Jt,_ as se,N as He,O as _r,P as Zt,Q as Bs,b as be,d as et,e as Es,c as kr,a as Cr}from"./Modal.vue_vue_type_script_setup_true_lang-79d5f001.js";import{r as $r,u as ge,a as Be,_ as Ae,b as qe,c as Sr,d as Mr,e as Qe,f as Ns,g as $e,L as Ue,N as Ds,A as Ft,h as Ps,i as Rs,j as Ar,k as jr}from"./Container.vue_vue_type_script_setup_true_lang-6937fcf6.js";function Tr(t,s){return c(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9"})])}function Ir(t,s){return c(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"})])}function ds(t,s){return c(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"})])}function us(t,s){return c(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"})])}function jt(t,s){return c(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"})])}function Tt(t,s){return c(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function dt(t,s){return c(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"})])}function Br(t,s){return c(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"})])}function Er(t,s){return c(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"})])}var Nr=typeof global=="object"&&global&&global.Object===Object&&global;const Dr=Nr;var Pr=typeof self=="object"&&self&&self.Object===Object&&self,Rr=Dr||Pr||Function("return this")();const Ls=Rr;var Lr=Ls.Symbol;const pt=Lr;var zs=Object.prototype,zr=zs.hasOwnProperty,Or=zs.toString,Je=pt?pt.toStringTag:void 0;function Fr(t){var s=zr.call(t,Je),r=t[Je];try{t[Je]=void 0;var o=!0}catch{}var a=Or.call(t);return o&&(s?t[Je]=r:delete t[Je]),a}var Vr=Object.prototype,qr=Vr.toString;function Ur(t){return qr.call(t)}var Hr="[object Null]",Wr="[object Undefined]",cs=pt?pt.toStringTag:void 0;function Kr(t){return t==null?t===void 0?Wr:Hr:cs&&cs in Object(t)?Fr(t):Ur(t)}function Yr(t){return t!=null&&typeof t=="object"}var Qr="[object Symbol]";function Xr(t){return typeof t=="symbol"||Yr(t)&&Kr(t)==Qr}var Gr=/\s/;function Jr(t){for(var s=t.length;s--&&Gr.test(t.charAt(s)););return s}var Zr=/^\s+/;function eo(t){return t&&t.slice(0,Jr(t)+1).replace(Zr,"")}function Vt(t){var s=typeof t;return t!=null&&(s=="object"||s=="function")}var ms=0/0,to=/^[-+]0x[0-9a-f]+$/i,so=/^0b[01]+$/i,ro=/^0o[0-7]+$/i,oo=parseInt;function fs(t){if(typeof t=="number")return t;if(Xr(t))return ms;if(Vt(t)){var s=typeof t.valueOf=="function"?t.valueOf():t;t=Vt(s)?s+"":s}if(typeof t!="string")return t===0?t:+t;t=eo(t);var r=so.test(t);return r||ro.test(t)?oo(t.slice(2),r?2:8):to.test(t)?ms:+t}var ao=function(){return Ls.Date.now()};const It=ao;var no="Expected a function",io=Math.max,lo=Math.min;function uo(t,s,r){var o,a,n,u,b,w,l=0,d=!1,I=!1,B=!0;if(typeof t!="function")throw new TypeError(no);s=fs(s)||0,Vt(r)&&(d=!!r.leading,I="maxWait"in r,n=I?io(fs(r.maxWait)||0,s):n,B="trailing"in r?!!r.trailing:B);function T(j){var A=o,O=a;return o=a=void 0,l=j,u=t.apply(O,A),u}function y(j){return l=j,b=setTimeout(R,s),d?T(j):u}function f(j){var A=j-w,O=j-l,Z=s-A;return I?lo(Z,n-O):Z}function _(j){var A=j-w,O=j-l;return w===void 0||A>=s||A<0||I&&O>=n}function R(){var j=It();if(_(j))return M(j);b=setTimeout(R,f(j))}function M(j){return b=void 0,B&&o?T(j):(o=a=void 0,u)}function g(){b!==void 0&&clearTimeout(b),l=0,o=w=a=b=void 0}function v(){return b===void 0?u:M(It())}function x(){var j=It(),A=_(j);if(o=arguments,a=this,w=j,A){if(b===void 0)return y(w);if(I)return clearTimeout(b),b=setTimeout(R,s),T(w)}return b===void 0&&(b=setTimeout(R,s)),u}return x.cancel=g,x.flush=v,x}const co={class:"overflow-hidden"},mo={key:0,class:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},fo=["checked","indeterminate"],po=["onClick"],vo={key:0},go=["colspan"],ho={key:1},yo=["colspan"],bo={key:0,class:"px-4 py-3"},xo=["checked","onChange"],wo=["innerHTML"],_o={key:1,class:"text-gray-900"},ko=pe({__name:"Table",props:{columns:{},data:{},loading:{type:Boolean,default:!1},striped:{type:Boolean,default:!1},bordered:{type:Boolean,default:!1},hover:{type:Boolean,default:!0},size:{default:"md"},sortBy:{},sortDirection:{default:"asc"},selectable:{type:Boolean,default:!1},selectedRows:{default:()=>[]},emptyMessage:{default:"No data available"},stickyHeader:{type:Boolean,default:!1},maxHeight:{}},emits:["sort","select","row-click"],setup(t,{emit:s}){const r=t,o=s,a=P([...r.selectedRows]),n=q(()=>"min-w-full divide-y divide-gray-200"),u=q(()=>{const M="bg-gray-50",g=r.stickyHeader?"sticky top-0 z-10":"";return[M,g].filter(Boolean).join(" ")}),b=q(()=>"bg-white divide-y divide-gray-200"),w={sm:"text-sm",md:"text-sm",lg:"text-base"},l=q(()=>r.columns.length+(r.selectable?1:0)),d=q(()=>r.data.length>0&&a.value.length===r.data.length),I=q(()=>a.value.length>0&&a.value.length<r.data.length),B=(M,g)=>g.split(".").reduce((v,x)=>v==null?void 0:v[x],M),T=(M,g)=>M.id||M.key||g,y=M=>{const g=r.sortBy===M&&r.sortDirection==="asc"?"desc":"asc";o("sort",{column:M,direction:g})},f=M=>a.value.some(g=>T(g,-1)===T(M,-1)),_=M=>{const g=f(M);g?a.value=a.value.filter(v=>T(v,-1)!==T(M,-1)):a.value.push(M),o("select",{selectedRows:[...a.value],row:M,isSelected:!g})},R=()=>{d.value?a.value=[]:a.value=[...r.data],o("select",{selectedRows:[...a.value]})};return(M,g)=>(c(),p("div",co,[e("div",{class:J(["overflow-auto",M.maxHeight?`max-h-[${M.maxHeight}]`:"",M.bordered?"border border-gray-200 rounded-lg":""])},[e("table",{class:J(n.value)},[e("thead",{class:J(u.value)},[e("tr",null,[M.selectable?(c(),p("th",mo,[e("input",{type:"checkbox",checked:d.value,indeterminate:I.value,onChange:R,class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,fo)])):z("",!0),(c(!0),p(me,null,fe(M.columns,v=>(c(),p("th",{key:v.key,class:J(["px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",v.align==="center"?"text-center":v.align==="right"?"text-right":"text-left",v.sortable?"cursor-pointer hover:bg-gray-100 select-none":"",v.headerClass||""]),style:rt(v.width?{width:v.width}:{}),onClick:x=>v.sortable?y(v.key):null},[e("div",{class:J(["flex items-center",v.align==="center"?"justify-center":v.align==="right"?"justify-end":"justify-start"])},[e("span",null,S(v.label),1),v.sortable?(c(),p(me,{key:0},[M.sortBy===v.key&&M.sortDirection==="asc"?(c(),ce(h(Ir),{key:0,class:"ml-1 h-4 w-4"})):M.sortBy===v.key&&M.sortDirection==="desc"?(c(),ce(h($r),{key:1,class:"ml-1 h-4 w-4"})):(c(),ce(h(Tr),{key:2,class:"ml-1 h-4 w-4 text-gray-400"}))],64)):z("",!0)],2)],14,po))),128))])],2),e("tbody",{class:J(b.value)},[M.loading?(c(),p("tr",vo,[e("td",{colspan:l.value,class:"px-4 py-8 text-center"},g[0]||(g[0]=[ze('<div class="flex items-center justify-center"><svg class="animate-spin h-5 w-5 text-primary-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><span class="text-gray-500">Loading...</span></div>',1)]),8,go)])):M.data.length===0?(c(),p("tr",ho,[e("td",{colspan:l.value,class:"px-4 py-8 text-center text-gray-500"},S(M.emptyMessage),9,yo)])):(c(!0),p(me,{key:2},fe(M.data,(v,x)=>(c(),p("tr",{key:T(v,x),class:J(["transition-colors duration-150",M.hover?"hover:bg-gray-50":"",M.striped&&x%2===1?"bg-gray-50":"",f(v)?"bg-primary-50":""])},[M.selectable?(c(),p("td",bo,[e("input",{type:"checkbox",checked:f(v),onChange:j=>_(v),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,xo)])):z("",!0),(c(!0),p(me,null,fe(M.columns,j=>(c(),p("td",{key:j.key,class:J(["px-4 py-3",j.align==="center"?"text-center":j.align==="right"?"text-right":"text-left",w[M.size],j.cellClass||""])},[j.render?(c(),p(me,{key:0},[typeof j.render(B(v,j.key),v,x)=="object"?(c(),ce(ot(j.render(B(v,j.key),v,x)),{key:0})):(c(),p("span",{key:1,innerHTML:j.render(B(v,j.key),v,x)},null,8,wo))],64)):(c(),p("span",_o,S(B(v,j.key)),1))],2))),128))],2))),128))],2)],2)],2)]))}}),nt=at("watchlist",()=>{const t=P([]),s=P(!1),r=P(null),o=P(0),a=()=>{window.dispatchEvent(new CustomEvent("watchlist-updated",{detail:{count:t.value.length}}))},n=q(()=>t.value.length),u=q(()=>t.value.length===0),b=q(()=>t.value.map(g=>g.id)),w=async(g=1,v=15,x="")=>{var A;const j=ge();if(!j.isAuthenticated)return t.value=[],{data:[],meta:{}};s.value=!0,r.value=null;try{const O=new URLSearchParams({page:g.toString(),per_page:v.toString(),...x&&{search:x}}),Z={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},oe=(A=document.querySelector('meta[name="csrf-token"]'))==null?void 0:A.getAttribute("content");oe&&(Z["X-CSRF-TOKEN"]=oe),j.token&&(Z.Authorization=`Bearer ${j.token}`);const de=await fetch(`/api/watchlist?${O}`,{headers:Z,credentials:"include"});if(!de.ok)throw new Error("Failed to fetch watchlist");const te=await de.json();return g===1?t.value=te.data||[]:t.value.push(...te.data||[]),o.value=Date.now(),a(),te}catch(O){return r.value=O instanceof Error?O.message:"Failed to fetch watchlist",console.error("Watchlist fetch error:",O),{data:[],meta:{}}}finally{s.value=!1}},l=async g=>{var x;const v=ge();if(!v.isAuthenticated)return r.value="Please sign in to add items to your watchlist",!1;y(g.id)||t.value.unshift(g);try{const j={"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"},A=(x=document.querySelector('meta[name="csrf-token"]'))==null?void 0:x.getAttribute("content");A&&(j["X-CSRF-TOKEN"]=A),v.token&&(j.Authorization=`Bearer ${v.token}`);const O=await fetch("/api/watchlist",{method:"POST",headers:j,credentials:"include",body:JSON.stringify({item_id:g.id})}),Z=await O.json();if(!O.ok)throw f(g.id),new Error(Z.message||"Failed to add item to watchlist");return r.value=null,a(),!0}catch(j){return f(g.id),r.value=j instanceof Error?j.message:"Failed to add item to watchlist",console.error("Add to watchlist error:",j),!1}},d=async g=>{var j;const v=ge();if(!v.isAuthenticated)return r.value="Please sign in to manage your watchlist",!1;const x=[...t.value];f(g);try{const A={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},O=(j=document.querySelector('meta[name="csrf-token"]'))==null?void 0:j.getAttribute("content");O&&(A["X-CSRF-TOKEN"]=O),v.token&&(A.Authorization=`Bearer ${v.token}`);const Z=await fetch(`/api/watchlist/${g}`,{method:"DELETE",headers:A,credentials:"include"}),oe=await Z.json();if(!Z.ok)throw t.value=x,new Error(oe.message||"Failed to remove item from watchlist");return r.value=null,a(),!0}catch(A){return t.value=x,r.value=A instanceof Error?A.message:"Failed to remove item from watchlist",console.error("Remove from watchlist error:",A),!1}},I=async g=>y(g.id)?await d(g.id):await l(g),B=async g=>{var x;const v=ge();if(!v.isAuthenticated)return!1;try{const j={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},A=(x=document.querySelector('meta[name="csrf-token"]'))==null?void 0:x.getAttribute("content");A&&(j["X-CSRF-TOKEN"]=A),v.token&&(j.Authorization=`Bearer ${v.token}`);const O=await fetch(`/api/watchlist/check/${g}`,{headers:j,credentials:"include"});return O.ok&&(await O.json()).in_watchlist||!1}catch(j){return console.error("Check watchlist status error:",j),!1}},T=async()=>{var v;const g=ge();if(!g.isAuthenticated)return t.value=[],0;try{const x={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},j=(v=document.querySelector('meta[name="csrf-token"]'))==null?void 0:v.getAttribute("content");j&&(x["X-CSRF-TOKEN"]=j),g.token&&(x.Authorization=`Bearer ${g.token}`);const A=await fetch("/api/watchlist/count",{headers:x,credentials:"include"});if(!A.ok)return t.value.length;const Z=(await A.json()).count||0;return Z!==t.value.length&&await w(),Z}catch(x){return console.error("Sync watchlist count error:",x),t.value.length}},y=g=>t.value.some(v=>v.id===g),f=g=>{const v=t.value.findIndex(x=>x.id===g);v>-1&&t.value.splice(v,1)};return{items:t,isLoading:s,error:r,lastSyncTime:o,watchlistCount:n,isEmpty:u,watchlistItemIds:b,fetchWatchlist:w,addToWatchlist:l,removeFromWatchlist:d,toggleWatchlist:I,checkWatchlistStatus:B,syncWatchlistCount:T,isInWatchlist:y,clearWatchlist:()=>{t.value=[],r.value=null,o.value=0,a()},refreshWatchlist:async()=>await w(),initializeWatchlist:async()=>{ge().isAuthenticated&&await w()}}}),Co={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"auth-modal-title",role:"dialog","aria-modal":"true"},$o={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},So={key:0,class:"inline-block align-bottom bg-white rounded-2xl shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full overflow-hidden"},Mo={class:"relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 px-6 py-8 text-center"},Ao={class:"mb-4"},jo={class:"mx-auto w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"},To={id:"auth-modal-title",class:"text-2xl font-bold text-white mb-2"},Io={class:"text-slate-300 text-sm"},Bo={class:"relative bg-white"},Eo={class:"px-6 py-8"},No={key:"login",class:"space-y-6"},Do={class:"relative"},Po={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},Ro=["disabled"],Lo={class:"relative"},zo={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},Oo=["type","disabled"],Fo={class:"flex items-center justify-between"},Vo={class:"flex items-center"},qo={class:"text-center"},Uo={class:"text-sm text-gray-600"},Ho={key:0,class:"bg-green-50 border border-green-200 rounded-xl p-4"},Wo={class:"flex"},Ko={class:"ml-3"},Yo={class:"text-sm text-green-800"},Qo={key:1,class:"bg-red-50 border border-red-200 rounded-xl p-4"},Xo={class:"flex"},Go={class:"ml-3"},Jo={class:"text-sm text-red-800"},Zo=["disabled"],ea={key:0},ta={key:1,class:"flex items-center"},sa={key:"register",class:"space-y-6"},ra={class:"grid grid-cols-2 gap-4"},oa=["disabled"],aa=["disabled"],na={class:"relative"},ia={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},la=["disabled"],da={key:0},ua={class:"relative"},ca={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},ma=["required","disabled"],fa={class:"grid grid-cols-1 gap-4"},pa={class:"relative"},va={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},ga=["type","disabled"],ha={class:"relative"},ya={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},ba=["type","disabled"],xa={class:"flex items-start"},wa={class:"flex items-center h-5"},_a={key:1,class:"bg-green-50 border border-green-200 rounded-xl p-4"},ka={class:"flex"},Ca={class:"ml-3"},$a={class:"text-sm text-green-800"},Sa={key:2,class:"bg-red-50 border border-red-200 rounded-xl p-4"},Ma={class:"flex"},Aa={class:"ml-3"},ja={class:"text-sm text-red-800"},Ta=["disabled"],Ia={key:0},Ba={key:1,class:"flex items-center"},Ea={class:"text-center"},Na={class:"text-sm text-gray-600"},Da=pe({__name:"AuthModal",props:{show:{type:Boolean},startWithRegister:{type:Boolean,default:!1},title:{default:""},subtitle:{default:""},closable:{type:Boolean,default:!0},closeOnBackdrop:{type:Boolean,default:!0},showPhoneField:{type:Boolean,default:!0},showSocialLogin:{type:Boolean,default:!1},autoFocus:{type:Boolean,default:!0},customBranding:{}},emits:["update:show","success","close","forgot-password","social-login"],setup(t,{expose:s,emit:r}){const o=t,a=r,n=ge(),u=Be(),b=P(o.startWithRegister),w=P(!1),l=P(!1),d=P(""),I=P(""),B=P({email:"",password:"",remember:!1}),T=P({firstName:"",lastName:"",email:"",phone:"",password:"",passwordConfirmation:"",acceptTerms:!1,acceptMarketing:!1}),y=q(()=>n.isLoading),f=q(()=>o.title?o.title:b.value?"Join Us Today":"Welcome Back"),_=q(()=>o.subtitle?o.subtitle:b.value?"Create an account to get started":"Sign in to your account to continue"),R=q(()=>T.value.firstName.trim()&&T.value.lastName.trim()&&T.value.email.trim()&&T.value.phone.trim()&&T.value.password&&T.value.passwordConfirmation&&T.value.password===T.value.passwordConfirmation&&T.value.password.length>=4&&T.value.acceptTerms),M=()=>{b.value=!b.value,x(),j(),o.autoFocus&&setTimeout(async()=>{await de()},150)},g=()=>{d.value=""},v=()=>{I.value=""},x=()=>{g(),v()},j=()=>{B.value={email:"",password:"",remember:!1},T.value={firstName:"",lastName:"",email:"",phone:"",password:"",passwordConfirmation:"",acceptTerms:!1,acceptMarketing:!1},w.value=!1,l.value=!1},A=async()=>{var te;if(x(),!B.value.email.trim()){d.value="Email address is required.",u.error("Email address is required.");return}if(!B.value.password){d.value="Password is required.",u.error("Password is required.");return}n.setLoading(!0);try{const V=(te=document.querySelector('meta[name="csrf-token"]'))==null?void 0:te.getAttribute("content");if(!V){const ne="Security token not found. Please refresh the page.";d.value=ne,u.error(ne);return}const Y=await fetch("/login",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":V},body:JSON.stringify({username:B.value.email,password:B.value.password,remember:B.value.remember,redirect:window.location.href})});if(Y.ok){const ne=await Y.json();I.value="Login successful! Redirecting...",u.success("Login successful! Welcome back."),a("success",ne.user||{email:B.value.email}),setTimeout(()=>{a("close"),a("update:show",!1),ne.redirect?window.location.href=ne.redirect:window.location.reload()},1e3)}else{const ne=await Y.json();let L="Invalid login credentials. Please check your email and password.";if(Y.status===422&&ne.errors){const $=[];for(const F in ne.errors)$.push(...ne.errors[F]);L=$.join(" ")}else ne.message&&(L=ne.message);d.value=L,u.error(L)}}catch(V){console.error("Login error:",V);let Y="Login failed. Please try again.";V instanceof TypeError&&V.message.includes("fetch")?Y="Network error. Please check your connection and try again.":V instanceof Error&&(Y=V.message),d.value=Y,u.error(Y)}finally{n.setLoading(!1)}},O=async()=>{var te;if(x(),!T.value.firstName.trim()){d.value="First name is required.";return}if(!T.value.lastName.trim()){d.value="Last name is required.";return}if(!T.value.email.trim()){d.value="Email address is required.";return}if(!T.value.phone.trim()){d.value="Phone number is required.";return}if(!T.value.password){d.value="Password is required.";return}if(T.value.password.length<4){d.value="Password must be at least 4 characters long.";return}if(T.value.password!==T.value.passwordConfirmation){d.value="Passwords do not match.";return}if(!T.value.acceptTerms){d.value="You must accept the terms and conditions.";return}n.setLoading(!0);try{const V=(te=document.querySelector('meta[name="csrf-token"]'))==null?void 0:te.getAttribute("content"),Y={name:`${T.value.firstName.trim()} ${T.value.lastName.trim()}`,email:T.value.email.trim(),phone:T.value.phone.trim(),password:T.value.password,password_confirmation:T.value.passwordConfirmation},ne=await fetch("/api/register",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":V||""},body:JSON.stringify(Y)}),L=await ne.json();if(!ne.ok){if(ne.status===422&&L.errors){const $=[];for(const F in L.errors)$.push(...L.errors[F]);d.value=$.join(" ")}else d.value=L.message||"Registration failed. Please try again.";return}I.value="Account created successfully! Logging you in...",u.success("Account created successfully! Logging you in..."),B.value.email=T.value.email,B.value.password=T.value.password,await A()}catch(V){console.error("Registration error:",V);let Y="Registration failed. Please try again.";V instanceof TypeError&&V.message.includes("fetch")?Y="Network error. Please check your connection and try again.":V instanceof Error&&(Y=V.message),d.value=Y,u.error(Y)}finally{n.setLoading(!1)}},Z=()=>{x(),j(),b.value=o.startWithRegister,a("close"),a("update:show",!1)},oe=()=>{o.closeOnBackdrop&&Z()},de=async()=>{if(!o.autoFocus)return;await Is();const te=b.value?"register-firstName":"login-email",V=document.getElementById(te);V&&V.focus()};return Me(()=>o.startWithRegister,te=>{b.value=te}),Me(()=>o.show,async te=>{te&&(b.value=o.startWithRegister,g(),j(),o.autoFocus&&setTimeout(async()=>{await de()},200))}),s({toggleForm:M,clearError:g,resetForms:j,autoFocusFirstInput:de}),(te,V)=>(c(),ce(br,{to:"body"},[D(Mt,{"enter-active-class":"duration-300 ease-out","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"duration-200 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:H(()=>[te.show?(c(),p("div",Co,[e("div",$o,[e("div",{class:"fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity backdrop-blur-sm",onClick:oe}),D(Mt,{"enter-active-class":"duration-300 ease-out","enter-from-class":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to-class":"opacity-100 translate-y-0 sm:scale-100","leave-active-class":"duration-200 ease-in","leave-from-class":"opacity-100 translate-y-0 sm:scale-100","leave-to-class":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:H(()=>[te.show?(c(),p("div",So,[e("div",Mo,[te.closable?(c(),p("button",{key:0,type:"button",class:"absolute top-4 right-4 text-white/80 hover:text-white transition-colors duration-200 rounded-full p-2 hover:bg-white/10",onClick:Z},[D(h(xr),{class:"h-5 w-5"})])):z("",!0),e("div",Ao,[e("div",jo,[D(h(Er),{class:"h-8 w-8 text-white"})])]),e("h2",To,S(f.value),1),e("p",Io,S(_.value),1)]),e("div",Bo,[e("div",Eo,[D(Mt,{mode:"out-in","enter-active-class":"duration-300 ease-out","enter-from-class":"opacity-0 translate-x-4","enter-to-class":"opacity-100 translate-x-0","leave-active-class":"duration-200 ease-in","leave-from-class":"opacity-100 translate-x-0","leave-to-class":"opacity-0 -translate-x-4"},{default:H(()=>[b.value?b.value?(c(),p("div",sa,[e("form",{onSubmit:Ce(O,["prevent"]),class:"space-y-5"},[e("div",ra,[e("div",null,[V[21]||(V[21]=e("label",{for:"register-firstName",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," First Name ",-1)),he(e("input",{id:"register-firstName","onUpdate:modelValue":V[6]||(V[6]=Y=>T.value.firstName=Y),type:"text",required:"",class:"block w-full px-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"First name",disabled:y.value},null,8,oa),[[Pe,T.value.firstName]])]),e("div",null,[V[22]||(V[22]=e("label",{for:"register-lastName",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Last Name ",-1)),he(e("input",{id:"register-lastName","onUpdate:modelValue":V[7]||(V[7]=Y=>T.value.lastName=Y),type:"text",required:"",class:"block w-full px-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Last name",disabled:y.value},null,8,aa),[[Pe,T.value.lastName]])])]),e("div",null,[V[23]||(V[23]=e("label",{for:"register-email",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Email Address ",-1)),e("div",na,[e("div",ia,[D(h(ds),{class:"h-5 w-5 text-gray-400"})]),he(e("input",{id:"register-email","onUpdate:modelValue":V[8]||(V[8]=Y=>T.value.email=Y),type:"email",required:"",class:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your email",disabled:y.value},null,8,la),[[Pe,T.value.email]])])]),te.showPhoneField?(c(),p("div",da,[V[24]||(V[24]=e("label",{for:"register-phone",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Phone Number ",-1)),e("div",ua,[e("div",ca,[D(h(Br),{class:"h-5 w-5 text-gray-400"})]),he(e("input",{id:"register-phone","onUpdate:modelValue":V[9]||(V[9]=Y=>T.value.phone=Y),type:"tel",required:!T.value.email,class:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your phone number",disabled:y.value},null,8,ma),[[Pe,T.value.phone]])])])):z("",!0),e("div",fa,[e("div",null,[V[25]||(V[25]=e("label",{for:"register-password",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Password ",-1)),e("div",pa,[e("div",va,[D(h(dt),{class:"h-5 w-5 text-gray-400"})]),he(e("input",{id:"register-password","onUpdate:modelValue":V[10]||(V[10]=Y=>T.value.password=Y),type:w.value?"text":"password",required:"",class:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Create a password",disabled:y.value},null,8,ga),[[At,T.value.password]]),e("button",{type:"button",class:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:V[11]||(V[11]=Y=>w.value=!w.value)},[w.value?(c(),ce(h(jt),{key:1,class:"h-5 w-5 text-gray-400 hover:text-gray-600"})):(c(),ce(h(Tt),{key:0,class:"h-5 w-5 text-gray-400 hover:text-gray-600"}))])])]),e("div",null,[V[26]||(V[26]=e("label",{for:"register-password-confirmation",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Confirm Password ",-1)),e("div",ha,[e("div",ya,[D(h(dt),{class:"h-5 w-5 text-gray-400"})]),he(e("input",{id:"register-password-confirmation","onUpdate:modelValue":V[12]||(V[12]=Y=>T.value.passwordConfirmation=Y),type:l.value?"text":"password",required:"",class:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Confirm your password",disabled:y.value},null,8,ba),[[At,T.value.passwordConfirmation]]),e("button",{type:"button",class:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:V[13]||(V[13]=Y=>l.value=!l.value)},[l.value?(c(),ce(h(jt),{key:1,class:"h-5 w-5 text-gray-400 hover:text-gray-600"})):(c(),ce(h(Tt),{key:0,class:"h-5 w-5 text-gray-400 hover:text-gray-600"}))])])])]),e("div",xa,[e("div",wa,[he(e("input",{id:"accept-terms","onUpdate:modelValue":V[14]||(V[14]=Y=>T.value.acceptTerms=Y),type:"checkbox",required:"",class:"h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 rounded"},null,512),[[Ye,T.value.acceptTerms]])]),V[27]||(V[27]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"accept-terms",class:"text-gray-700"},[W(" I agree to the "),e("button",{type:"button",class:"text-slate-600 hover:text-slate-500 font-medium"}," Terms of Service "),W(" and "),e("button",{type:"button",class:"text-slate-600 hover:text-slate-500 font-medium"}," Privacy Policy ")])],-1))]),I.value?(c(),p("div",_a,[e("div",ka,[D(h(ls),{class:"h-5 w-5 text-green-400"}),e("div",Ca,[e("p",$a,S(I.value),1)])])])):z("",!0),d.value?(c(),p("div",Sa,[e("div",Ma,[D(h(us),{class:"h-5 w-5 text-red-400"}),e("div",Aa,[e("p",ja,S(d.value),1)])])])):z("",!0),e("button",{type:"submit",disabled:y.value||!R.value,class:"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-slate-700 to-slate-900 hover:from-slate-800 hover:to-slate-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"},[y.value?(c(),p("span",Ba,V[28]||(V[28]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),W(" Creating account... ")]))):(c(),p("span",Ia,"Create Account"))],8,Ta)],32),e("div",Ea,[e("p",Na,[V[29]||(V[29]=W(" Already have an account? ")),e("button",{type:"button",class:"font-medium text-slate-600 hover:text-slate-500 transition-colors duration-200",onClick:V[15]||(V[15]=Y=>b.value=!1)}," Sign in here ")])])])):z("",!0):(c(),p("div",No,[e("form",{onSubmit:Ce(A,["prevent"]),class:"space-y-5"},[e("div",null,[V[16]||(V[16]=e("label",{for:"login-email",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Email Address ",-1)),e("div",Do,[e("div",Po,[D(h(ds),{class:"h-5 w-5 text-gray-400"})]),he(e("input",{id:"login-email","onUpdate:modelValue":V[0]||(V[0]=Y=>B.value.email=Y),type:"email",required:"",class:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your email",disabled:y.value},null,8,Ro),[[Pe,B.value.email]])])]),e("div",null,[V[17]||(V[17]=e("label",{for:"login-password",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Password ",-1)),e("div",Lo,[e("div",zo,[D(h(dt),{class:"h-5 w-5 text-gray-400"})]),he(e("input",{id:"login-password","onUpdate:modelValue":V[1]||(V[1]=Y=>B.value.password=Y),type:w.value?"text":"password",required:"",class:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your password",disabled:y.value},null,8,Oo),[[At,B.value.password]]),e("button",{type:"button",class:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:V[2]||(V[2]=Y=>w.value=!w.value)},[w.value?(c(),ce(h(jt),{key:1,class:"h-5 w-5 text-gray-400 hover:text-gray-600"})):(c(),ce(h(Tt),{key:0,class:"h-5 w-5 text-gray-400 hover:text-gray-600"}))])])]),e("div",Fo,[e("div",Vo,[he(e("input",{id:"remember-me","onUpdate:modelValue":V[3]||(V[3]=Y=>B.value.remember=Y),type:"checkbox",class:"h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 rounded"},null,512),[[Ye,B.value.remember]]),V[18]||(V[18]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-700"}," Remember me ",-1))]),e("button",{type:"button",class:"text-sm text-slate-600 hover:text-slate-500 font-medium",onClick:V[4]||(V[4]=Y=>te.$emit("forgot-password"))}," Forgot password? ")]),e("div",qo,[e("p",Uo,[V[19]||(V[19]=W(" Don't have an account? ")),e("button",{type:"button",class:"font-medium text-slate-600 hover:text-slate-500 transition-colors duration-200",onClick:V[5]||(V[5]=Y=>b.value=!0)}," Create one here ")])]),I.value?(c(),p("div",Ho,[e("div",Wo,[D(h(ls),{class:"h-5 w-5 text-green-400"}),e("div",Ko,[e("p",Yo,S(I.value),1)])])])):z("",!0),d.value?(c(),p("div",Qo,[e("div",Xo,[D(h(us),{class:"h-5 w-5 text-red-400"}),e("div",Go,[e("p",Jo,S(d.value),1)])])])):z("",!0),e("button",{type:"submit",disabled:y.value||!B.value.email||!B.value.password,class:"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-slate-700 to-slate-900 hover:from-slate-800 hover:to-slate-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"},[y.value?(c(),p("span",ta,V[20]||(V[20]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),W(" Signing in... ")]))):(c(),p("span",ea,"Sign In"))],8,Zo)],32)]))]),_:1})])])])):z("",!0)]),_:1})])])):z("",!0)]),_:1})]))}}),yt=Ae(Da,[["__scopeId","data-v-da7d539c"]]),Pa={key:0,class:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"},Ra=pe({__name:"WatchlistNavIcon",setup(t){const s=nt(),r=ge(),o=qe(),a=P(!1),n=q(()=>{const w=window.location.pathname;return w==="/cart"||w.startsWith("/spa")||w.startsWith("/home-vue")||w.startsWith("/bid-dashboard")}),u=w=>{if(!r.isAuthenticated){w.preventDefault(),a.value=!0;return}n.value&&(w.preventDefault(),o.push("/bid-dashboard"))},b=async w=>{a.value=!1,await s.initializeWatchlist(),n.value?o.push("/bid-dashboard"):window.location.href="/bid-dashboard"};return(w,l)=>(c(),p(me,null,[(c(),ce(ot(n.value?"router-link":"a"),{to:n.value&&h(r).isAuthenticated?"/bid-dashboard":void 0,href:n.value||!h(r).isAuthenticated?void 0:"/bid-dashboard",onClick:u,class:"relative p-2 text-gray-600 hover:text-primary-600 rounded-lg hover:bg-white/50 transition-all duration-200",title:h(r).isAuthenticated?h(s).watchlistCount>0?`${h(s).watchlistCount} items in watchlist`:"View your watchlist":"Sign in to view your watchlist"},{default:H(()=>[l[1]||(l[1]=e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})],-1)),h(r).isAuthenticated&&h(s).watchlistCount>0?(c(),p("span",Pa,S(h(s).watchlistCount>9?"9+":h(s).watchlistCount),1)):z("",!0)]),_:1,__:[1]},8,["to","href","title"])),D(yt,{show:a.value,"onUpdate:show":l[0]||(l[0]=d=>a.value=d),title:"Sign in to view your watchlist",subtitle:"Track your favorite auction items and manage your bidding activity.",onSuccess:b},null,8,["show"])],64))}}),La=["aria-selected","aria-controls","id","disabled","onClick","onKeydown"],za=["onClick","aria-label"],Oa=["id","aria-labelledby","tabindex"],Fa=["innerHTML"],Va=pe({__name:"Tabs",props:{tabs:{},modelValue:{default:0},variant:{default:"default"},size:{default:"md"},vertical:{type:Boolean,default:!1},centered:{type:Boolean,default:!1},addable:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1}},emits:["update:modelValue","tab-change","tab-close","tab-add"],setup(t,{expose:s,emit:r}){const o=t,a=r,n=P(o.modelValue);Me(()=>o.modelValue,g=>{n.value=g}),Me(n,g=>{a("update:modelValue",g),a("tab-change",g,o.tabs[g])});const u=q(()=>{const g=["tabs-container"];return o.vertical&&g.push("flex"),g.join(" ")}),b=q(()=>{const g=["flex","tab-list"];o.vertical?g.push("flex-col","space-y-1","mr-4"):g.push("space-x-1"),o.centered&&!o.vertical&&g.push("justify-center");const v={default:["border-b","border-gray-200"],pills:[],underline:["border-b","border-gray-200"],bordered:["border","border-gray-200","rounded-lg","p-1","bg-gray-50"]};return[...g,...v[o.variant]].join(" ")}),w=g=>{const v=n.value===g,x=o.tabs[g],j=["flex","items-center","space-x-2","font-medium","transition-all","duration-200","focus:outline-none"];o.variant!=="underline"&&j.push("focus:ring-2","focus:ring-offset-2","focus:ring-indigo-500");const A={sm:["px-3","py-1.5","text-sm"],md:["px-4","py-2","text-sm"],lg:["px-6","py-3","text-base"]},O={default:v?["text-indigo-600","border-b-2","border-indigo-600"]:["text-gray-500","hover:text-gray-700","border-b-2","border-transparent"],pills:v?["bg-indigo-100","text-indigo-700","rounded-lg"]:["text-gray-500","hover:text-gray-700","hover:bg-gray-100","rounded-lg"],underline:v?["text-indigo-600","border-b-2","border-indigo-600"]:["text-gray-500","hover:text-gray-700","border-b-2","border-transparent"],bordered:v?["bg-white","text-indigo-700","shadow-sm","rounded-md"]:["text-gray-500","hover:text-gray-700","hover:bg-white","rounded-md"]},Z=x.disabled?["opacity-50","cursor-not-allowed","pointer-events-none"]:["cursor-pointer"];return[...j,...A[o.size],...O[o.variant],...Z].join(" ")},l=g=>{const v=n.value===g,x=["w-4","h-4"];return v?x.push("text-current"):x.push("text-gray-400"),x.join(" ")},d=g=>{const v=n.value===g,x=["inline-flex","items-center","justify-center","px-2","py-0.5","rounded-full","text-xs","font-medium","min-w-[1.25rem]","h-5"];return v?x.push("bg-indigo-100","text-indigo-800"):x.push("bg-gray-100","text-gray-600"),x.join(" ")},I=g=>{const v=n.value===g,x=["ml-2","p-0.5","rounded","hover:bg-gray-200","focus:outline-none","focus:ring-1","focus:ring-gray-400"];return v?x.push("text-indigo-500"):x.push("text-gray-400"),x.join(" ")},B=q(()=>["flex","items-center","justify-center","w-8","h-8","text-gray-400","hover:text-gray-600","hover:bg-gray-100","rounded","transition-colors","duration-200","focus:outline-none","focus:ring-2","focus:ring-offset-2","focus:ring-indigo-500"].join(" ")),T=q(()=>{const g=["tab-panels"];return o.vertical?g.push("flex-1"):g.push("mt-4"),g.join(" ")}),y=q(()=>["tab-panel","focus:outline-none"].join(" ")),f=g=>{var v;(v=o.tabs[g])!=null&&v.disabled||(n.value=g)},_=g=>{const v=o.tabs[g];if(a("tab-close",g,v),n.value===g){const x=g>0?g-1:0;o.tabs.length>1&&Is(()=>{n.value=Math.min(x,o.tabs.length-2)})}else n.value>g&&n.value--},R=()=>{a("tab-add")},M=(g,v)=>{const{key:x}=g;if(x==="ArrowLeft"||x==="ArrowUp"){g.preventDefault();const j=v>0?v-1:o.tabs.length-1;f(j)}else if(x==="ArrowRight"||x==="ArrowDown"){g.preventDefault();const j=v<o.tabs.length-1?v+1:0;f(j)}else x==="Home"?(g.preventDefault(),f(0)):x==="End"&&(g.preventDefault(),f(o.tabs.length-1))};return s({selectTab:f,closeTab:_,addTab:R,activeTab:()=>n.value}),(g,v)=>(c(),p("div",{class:J(u.value)},[e("div",{class:J(b.value),role:"tablist"},[(c(!0),p(me,null,fe(g.tabs,(x,j)=>(c(),p("button",{key:x.key||j,class:J(w(j)),"aria-selected":n.value===j,"aria-controls":`tabpanel-${x.key||j}`,id:`tab-${x.key||j}`,role:"tab",disabled:x.disabled,onClick:A=>f(j),onKeydown:A=>M(A,j)},[x.icon?(c(),ce(ot(x.icon),{key:0,class:J(l(j))},null,8,["class"])):z("",!0),e("span",null,S(x.label),1),x.badge!==void 0?(c(),p("span",{key:1,class:J(d(j))},S(x.badge),3)):z("",!0),x.closable?(c(),p("button",{key:2,class:J(I(j)),onClick:Ce(A=>_(j),["stop"]),"aria-label":`Close ${x.label} tab`},v[0]||(v[0]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,za)):z("",!0)],42,La))),128)),g.addable?(c(),p("button",{key:0,class:J(B.value),onClick:R,"aria-label":"Add new tab"},v[1]||(v[1]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1)]),2)):z("",!0)],2),e("div",{class:J(T.value)},[(c(!0),p(me,null,fe(g.tabs,(x,j)=>he((c(),p("div",{key:`panel-${x.key||j}`,class:J(y.value),id:`tabpanel-${x.key||j}`,"aria-labelledby":`tab-${x.key||j}`,role:"tabpanel",tabindex:n.value===j?0:-1},[tt(g.$slots,x.key||`tab-${j}`,{tab:x,index:j,active:n.value===j},()=>[x.content?(c(),p("div",{key:0,innerHTML:x.content},null,8,Fa)):z("",!0)],!0)],10,Oa)),[[wr,n.value===j]])),128))],2)],2))}}),qa=Ae(Va,[["__scopeId","data-v-c55ef745"]]),Ua={key:0,class:"banner-section"},Ha={class:"banner-container relative overflow-hidden"},Wa={class:"relative"},Ka={href:"/register-bid",class:"block w-full"},Ya=["src","alt"],Qa={key:0,class:"absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center"},Xa={class:"container mx-auto px-4"},Ga={class:"grid grid-cols-1 md:grid-cols-3 gap-4 items-center text-white"},Ja={class:"text-center"},Za={class:"countdown-timer"},en={class:"grid grid-cols-4 gap-2 text-center"},tn={class:"countdown-item"},sn={class:"text-3xl font-bold"},rn={class:"countdown-item"},on={class:"text-3xl font-bold"},an={class:"countdown-item"},nn={class:"text-3xl font-bold"},ln={class:"countdown-item"},dn={class:"text-3xl font-bold"},un={class:"text-center md:text-right"},cn={class:"flex items-center justify-center md:justify-end space-x-2"},mn={class:"text-sm"},fn={key:1,class:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6"},pn={class:"text-white text-center"},vn={class:"flex items-center justify-center space-x-2"},gn={key:0,class:"absolute bottom-4 left-1/2 transform -translate-x-1/2"},hn={class:"flex space-x-2"},yn=["onClick"],bn=pe({__name:"Banner",props:{adverts:{}},setup(t){const s=t,r=P(new Date);let o=null;const a=P(0),n=q(()=>{if(!s.adverts||s.adverts.length===0)return null;const l=a.value<s.adverts.length?a.value:0;return s.adverts[l]}),u=q(()=>{if(!n.value||!n.value.date_to)return{days:0,hours:0,minutes:0,seconds:0};const d=new Date(n.value.date_to).getTime()-r.value.getTime();if(d<=0)return{days:0,hours:0,minutes:0,seconds:0};const I=Math.floor(d/(1e3*60*60*24)),B=Math.floor(d%(1e3*60*60*24)/(1e3*60*60)),T=Math.floor(d%(1e3*60*60)/(1e3*60)),y=Math.floor(d%(1e3*60)/1e3);return{days:I,hours:B,minutes:T,seconds:y}}),b=()=>{r.value=new Date},w=l=>{l>=0&&l<s.adverts.length&&(a.value=l)};return Te(()=>{o=setInterval(b,1e3)}),Jt(()=>{o&&clearInterval(o)}),(l,d)=>l.adverts&&l.adverts.length>0&&n.value?(c(),p("div",Ua,[e("div",Ha,[e("div",Wa,[e("a",Ka,[e("img",{src:n.value.image,alt:n.value.description||"Advertisement",class:"w-full h-auto object-cover",style:{"max-height":"400px"}},null,8,Ya)]),n.value.date_to?(c(),p("div",Qa,[e("div",Xa,[e("div",Ga,[d[5]||(d[5]=e("div",{class:"hidden md:block"},null,-1)),e("div",Ja,[e("div",Za,[e("div",en,[e("div",tn,[e("div",sn,S(u.value.days),1),d[0]||(d[0]=e("div",{class:"text-sm uppercase tracking-wide"},"Days",-1))]),e("div",rn,[e("div",on,S(u.value.hours),1),d[1]||(d[1]=e("div",{class:"text-sm uppercase tracking-wide"},"Hours",-1))]),e("div",an,[e("div",nn,S(u.value.minutes),1),d[2]||(d[2]=e("div",{class:"text-sm uppercase tracking-wide"},"Minutes",-1))]),e("div",ln,[e("div",dn,S(u.value.seconds),1),d[3]||(d[3]=e("div",{class:"text-sm uppercase tracking-wide"},"Seconds",-1))])])])]),e("div",un,[e("div",cn,[d[4]||(d[4]=e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})],-1)),e("span",mn,S(n.value.description),1)])])])])])):n.value.description?(c(),p("div",fn,[e("div",pn,[e("div",vn,[d[6]||(d[6]=e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})],-1)),e("span",null,S(n.value.description),1)])])])):z("",!0)]),l.adverts.length>1?(c(),p("div",gn,[e("div",hn,[(c(!0),p(me,null,fe(l.adverts,(I,B)=>(c(),p("button",{key:I.id,onClick:T=>w(B),class:J(["w-3 h-3 rounded-full transition-all duration-300",a.value===B?"bg-white":"bg-white bg-opacity-50 hover:bg-opacity-75"])},null,10,yn))),128))])])):z("",!0)])])):z("",!0)}}),Os=Ae(bn,[["__scopeId","data-v-4bed1f7b"]]),xn={class:"relative"},wn={key:0,class:"flex justify-center items-center mt-6 space-x-4"},_n=["disabled"],kn={class:"flex space-x-2"},Cn=["onClick","aria-label"],$n=["disabled"],Sn={key:1,class:"text-center mt-2 text-sm text-gray-500"},Mn=50,An=pe({__name:"HorizontalSlider",props:{items:{},autoPlay:{type:Boolean,default:!1},autoPlayInterval:{default:5e3},showCounter:{type:Boolean,default:!0},containerClasses:{default:""},navigationButtonClasses:{default:""},dotClasses:{default:""},keyExtractor:{type:Function,default:(t,s)=>s}},setup(t){const s=t,r=P(),o=P(0),a=P(null),n=P(0),u=P(0),b=(g,v)=>s.keyExtractor(g,v),w=g=>{g>=0&&g<s.items.length&&(o.value=g,T())},l=()=>{o.value<s.items.length-1&&(o.value++,T())},d=()=>{o.value>0&&(o.value--,T())},I=()=>{!s.autoPlay||s.items.length<=1||(a.value=setInterval(()=>{o.value<s.items.length-1?o.value++:o.value=0},s.autoPlayInterval))},B=()=>{a.value&&(clearInterval(a.value),a.value=null)},T=()=>{s.autoPlay&&(B(),I())},y=g=>{n.value=g.touches[0].clientX},f=g=>{g.preventDefault()},_=g=>{u.value=g.changedTouches[0].clientX,R()},R=()=>{const g=n.value-u.value;Math.abs(g)>Mn&&(g>0?l():d())},M=g=>{switch(g.key){case"ArrowLeft":g.preventDefault(),d();break;case"ArrowRight":g.preventDefault(),l();break;case"Home":g.preventDefault(),w(0);break;case"End":g.preventDefault(),w(s.items.length-1);break}};return Me(()=>s.items,()=>{o.value=0,T()},{deep:!0}),Te(()=>{s.autoPlay&&I()}),Jt(()=>{B()}),(g,v)=>(c(),p("div",xn,[e("div",{ref_key:"sliderContainer",ref:r,class:J(["overflow-hidden focus:outline-none",g.containerClasses]),tabindex:"0",onKeydown:M,onTouchstart:y,onTouchmove:f,onTouchend:_},[e("div",{class:"flex transition-transform duration-300 ease-in-out",style:rt({transform:`translateX(-${o.value*100}%)`})},[(c(!0),p(me,null,fe(g.items,(x,j)=>(c(),p("div",{key:b(x,j),class:"w-full flex-shrink-0"},[tt(g.$slots,"default",{item:x,index:j,isActive:j===o.value},void 0,!0)]))),128))],4)],34),g.items.length>1?(c(),p("div",wn,[e("button",{onClick:d,disabled:o.value===0,class:J(["flex items-center justify-center w-10 h-10 rounded-full bg-white border border-gray-300 shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500",g.navigationButtonClasses]),"aria-label":"Previous item"},[D(h(Sr),{class:"h-5 w-5 text-gray-600"})],10,_n),e("div",kn,[(c(!0),p(me,null,fe(g.items,(x,j)=>(c(),p("button",{key:`dot-${b(x,j)}`,onClick:A=>w(j),class:J(["w-3 h-3 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",[j===o.value?"bg-blue-600 scale-110":"bg-gray-300 hover:bg-gray-400",g.dotClasses]]),"aria-label":`Go to item ${j+1}`},null,10,Cn))),128))]),e("button",{onClick:l,disabled:o.value===g.items.length-1,class:J(["flex items-center justify-center w-10 h-10 rounded-full bg-white border border-gray-300 shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500",g.navigationButtonClasses]),"aria-label":"Next item"},[D(h(Mr),{class:"h-5 w-5 text-gray-600"})],10,$n)])):z("",!0),g.showCounter&&g.items.length>1?(c(),p("div",Sn,S(o.value+1)+" of "+S(g.items.length),1)):z("",!0)]))}}),jn=Ae(An,[["__scopeId","data-v-bf9f80b5"]]),Ke=(t,s="MWK",r="en-MW",o={})=>{const{showDecimals:a=!0,compact:n=!1}=o;if(t===0)return s==="MWK"?"MK 0":`${s} 0`;if(s==="MWK"||s==="MK"){const b={minimumFractionDigits:a?2:0,maximumFractionDigits:a?2:0};return n&&t>=1e6&&(b.notation="compact",b.compactDisplay="short"),`MK ${new Intl.NumberFormat("en-MW",b).format(t)}`}const u={style:"currency",currency:s,minimumFractionDigits:a?2:0,maximumFractionDigits:a?2:0};return n&&t>=1e6&&(u.notation="compact",u.compactDisplay="short"),new Intl.NumberFormat(r,u).format(t)},Fs=(t,s={year:"numeric",month:"short",day:"numeric"},r="en-US")=>{const o=typeof t=="string"?new Date(t):t;return new Intl.DateTimeFormat(r,s).format(o)},qt=(t,s="en-US")=>{const r=typeof t=="string"?new Date(t):t,a=Math.floor((new Date().getTime()-r.getTime())/1e3),n=new Intl.RelativeTimeFormat(s,{numeric:"auto"});return a<60?n.format(-a,"second"):a<3600?n.format(-Math.floor(a/60),"minute"):a<86400?n.format(-Math.floor(a/3600),"hour"):a<2592e3?n.format(-Math.floor(a/86400),"day"):a<31536e3?n.format(-Math.floor(a/2592e3),"month"):n.format(-Math.floor(a/31536e3),"year")},ps=(t,s="MWK")=>Ke(t,s),Tn=t=>{const s=new Date,r=t.getTime()-s.getTime();if(r<=0)return"Ended";const o=Math.floor(r/(1e3*60*60*24)),a=Math.floor(r%(1e3*60*60*24)/(1e3*60*60)),n=Math.floor(r%(1e3*60*60)/(1e3*60));return o>0?`${o}d ${a}h`:a>0?`${a}h ${n}m`:`${n}m`};function Vs(t,s){return function(){return t.apply(s,arguments)}}const{toString:In}=Object.prototype,{getPrototypeOf:es}=Object,{iterator:bt,toStringTag:qs}=Symbol,xt=(t=>s=>{const r=In.call(s);return t[r]||(t[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),Ee=t=>(t=t.toLowerCase(),s=>xt(s)===t),wt=t=>s=>typeof s===t,{isArray:Xe}=Array,st=wt("undefined");function Bn(t){return t!==null&&!st(t)&&t.constructor!==null&&!st(t.constructor)&&Se(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const Us=Ee("ArrayBuffer");function En(t){let s;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?s=ArrayBuffer.isView(t):s=t&&t.buffer&&Us(t.buffer),s}const Nn=wt("string"),Se=wt("function"),Hs=wt("number"),_t=t=>t!==null&&typeof t=="object",Dn=t=>t===!0||t===!1,ut=t=>{if(xt(t)!=="object")return!1;const s=es(t);return(s===null||s===Object.prototype||Object.getPrototypeOf(s)===null)&&!(qs in t)&&!(bt in t)},Pn=Ee("Date"),Rn=Ee("File"),Ln=Ee("Blob"),zn=Ee("FileList"),On=t=>_t(t)&&Se(t.pipe),Fn=t=>{let s;return t&&(typeof FormData=="function"&&t instanceof FormData||Se(t.append)&&((s=xt(t))==="formdata"||s==="object"&&Se(t.toString)&&t.toString()==="[object FormData]"))},Vn=Ee("URLSearchParams"),[qn,Un,Hn,Wn]=["ReadableStream","Request","Response","Headers"].map(Ee),Kn=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function it(t,s,{allOwnKeys:r=!1}={}){if(t===null||typeof t>"u")return;let o,a;if(typeof t!="object"&&(t=[t]),Xe(t))for(o=0,a=t.length;o<a;o++)s.call(null,t[o],o,t);else{const n=r?Object.getOwnPropertyNames(t):Object.keys(t),u=n.length;let b;for(o=0;o<u;o++)b=n[o],s.call(null,t[b],b,t)}}function Ws(t,s){s=s.toLowerCase();const r=Object.keys(t);let o=r.length,a;for(;o-- >0;)if(a=r[o],s===a.toLowerCase())return a;return null}const Fe=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),Ks=t=>!st(t)&&t!==Fe;function Ut(){const{caseless:t}=Ks(this)&&this||{},s={},r=(o,a)=>{const n=t&&Ws(s,a)||a;ut(s[n])&&ut(o)?s[n]=Ut(s[n],o):ut(o)?s[n]=Ut({},o):Xe(o)?s[n]=o.slice():s[n]=o};for(let o=0,a=arguments.length;o<a;o++)arguments[o]&&it(arguments[o],r);return s}const Yn=(t,s,r,{allOwnKeys:o}={})=>(it(s,(a,n)=>{r&&Se(a)?t[n]=Vs(a,r):t[n]=a},{allOwnKeys:o}),t),Qn=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),Xn=(t,s,r,o)=>{t.prototype=Object.create(s.prototype,o),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:s.prototype}),r&&Object.assign(t.prototype,r)},Gn=(t,s,r,o)=>{let a,n,u;const b={};if(s=s||{},t==null)return s;do{for(a=Object.getOwnPropertyNames(t),n=a.length;n-- >0;)u=a[n],(!o||o(u,t,s))&&!b[u]&&(s[u]=t[u],b[u]=!0);t=r!==!1&&es(t)}while(t&&(!r||r(t,s))&&t!==Object.prototype);return s},Jn=(t,s,r)=>{t=String(t),(r===void 0||r>t.length)&&(r=t.length),r-=s.length;const o=t.indexOf(s,r);return o!==-1&&o===r},Zn=t=>{if(!t)return null;if(Xe(t))return t;let s=t.length;if(!Hs(s))return null;const r=new Array(s);for(;s-- >0;)r[s]=t[s];return r},ei=(t=>s=>t&&s instanceof t)(typeof Uint8Array<"u"&&es(Uint8Array)),ti=(t,s)=>{const o=(t&&t[bt]).call(t);let a;for(;(a=o.next())&&!a.done;){const n=a.value;s.call(t,n[0],n[1])}},si=(t,s)=>{let r;const o=[];for(;(r=t.exec(s))!==null;)o.push(r);return o},ri=Ee("HTMLFormElement"),oi=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,o,a){return o.toUpperCase()+a}),vs=(({hasOwnProperty:t})=>(s,r)=>t.call(s,r))(Object.prototype),ai=Ee("RegExp"),Ys=(t,s)=>{const r=Object.getOwnPropertyDescriptors(t),o={};it(r,(a,n)=>{let u;(u=s(a,n,t))!==!1&&(o[n]=u||a)}),Object.defineProperties(t,o)},ni=t=>{Ys(t,(s,r)=>{if(Se(t)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const o=t[r];if(Se(o)){if(s.enumerable=!1,"writable"in s){s.writable=!1;return}s.set||(s.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},ii=(t,s)=>{const r={},o=a=>{a.forEach(n=>{r[n]=!0})};return Xe(t)?o(t):o(String(t).split(s)),r},li=()=>{},di=(t,s)=>t!=null&&Number.isFinite(t=+t)?t:s;function ui(t){return!!(t&&Se(t.append)&&t[qs]==="FormData"&&t[bt])}const ci=t=>{const s=new Array(10),r=(o,a)=>{if(_t(o)){if(s.indexOf(o)>=0)return;if(!("toJSON"in o)){s[a]=o;const n=Xe(o)?[]:{};return it(o,(u,b)=>{const w=r(u,a+1);!st(w)&&(n[b]=w)}),s[a]=void 0,n}}return o};return r(t,0)},mi=Ee("AsyncFunction"),fi=t=>t&&(_t(t)||Se(t))&&Se(t.then)&&Se(t.catch),Qs=((t,s)=>t?setImmediate:s?((r,o)=>(Fe.addEventListener("message",({source:a,data:n})=>{a===Fe&&n===r&&o.length&&o.shift()()},!1),a=>{o.push(a),Fe.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",Se(Fe.postMessage)),pi=typeof queueMicrotask<"u"?queueMicrotask.bind(Fe):typeof process<"u"&&process.nextTick||Qs,vi=t=>t!=null&&Se(t[bt]),E={isArray:Xe,isArrayBuffer:Us,isBuffer:Bn,isFormData:Fn,isArrayBufferView:En,isString:Nn,isNumber:Hs,isBoolean:Dn,isObject:_t,isPlainObject:ut,isReadableStream:qn,isRequest:Un,isResponse:Hn,isHeaders:Wn,isUndefined:st,isDate:Pn,isFile:Rn,isBlob:Ln,isRegExp:ai,isFunction:Se,isStream:On,isURLSearchParams:Vn,isTypedArray:ei,isFileList:zn,forEach:it,merge:Ut,extend:Yn,trim:Kn,stripBOM:Qn,inherits:Xn,toFlatObject:Gn,kindOf:xt,kindOfTest:Ee,endsWith:Jn,toArray:Zn,forEachEntry:ti,matchAll:si,isHTMLForm:ri,hasOwnProperty:vs,hasOwnProp:vs,reduceDescriptors:Ys,freezeMethods:ni,toObjectSet:ii,toCamelCase:oi,noop:li,toFiniteNumber:di,findKey:Ws,global:Fe,isContextDefined:Ks,isSpecCompliantForm:ui,toJSONObject:ci,isAsyncFn:mi,isThenable:fi,setImmediate:Qs,asap:pi,isIterable:vi};function ae(t,s,r,o,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",s&&(this.code=s),r&&(this.config=r),o&&(this.request=o),a&&(this.response=a,this.status=a.status?a.status:null)}E.inherits(ae,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:E.toJSONObject(this.config),code:this.code,status:this.status}}});const Xs=ae.prototype,Gs={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{Gs[t]={value:t}});Object.defineProperties(ae,Gs);Object.defineProperty(Xs,"isAxiosError",{value:!0});ae.from=(t,s,r,o,a,n)=>{const u=Object.create(Xs);return E.toFlatObject(t,u,function(w){return w!==Error.prototype},b=>b!=="isAxiosError"),ae.call(u,t.message,s,r,o,a),u.cause=t,u.name=t.name,n&&Object.assign(u,n),u};const gi=null;function Ht(t){return E.isPlainObject(t)||E.isArray(t)}function Js(t){return E.endsWith(t,"[]")?t.slice(0,-2):t}function gs(t,s,r){return t?t.concat(s).map(function(a,n){return a=Js(a),!r&&n?"["+a+"]":a}).join(r?".":""):s}function hi(t){return E.isArray(t)&&!t.some(Ht)}const yi=E.toFlatObject(E,{},null,function(s){return/^is[A-Z]/.test(s)});function kt(t,s,r){if(!E.isObject(t))throw new TypeError("target must be an object");s=s||new FormData,r=E.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(f,_){return!E.isUndefined(_[f])});const o=r.metaTokens,a=r.visitor||d,n=r.dots,u=r.indexes,w=(r.Blob||typeof Blob<"u"&&Blob)&&E.isSpecCompliantForm(s);if(!E.isFunction(a))throw new TypeError("visitor must be a function");function l(y){if(y===null)return"";if(E.isDate(y))return y.toISOString();if(E.isBoolean(y))return y.toString();if(!w&&E.isBlob(y))throw new ae("Blob is not supported. Use a Buffer instead.");return E.isArrayBuffer(y)||E.isTypedArray(y)?w&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function d(y,f,_){let R=y;if(y&&!_&&typeof y=="object"){if(E.endsWith(f,"{}"))f=o?f:f.slice(0,-2),y=JSON.stringify(y);else if(E.isArray(y)&&hi(y)||(E.isFileList(y)||E.endsWith(f,"[]"))&&(R=E.toArray(y)))return f=Js(f),R.forEach(function(g,v){!(E.isUndefined(g)||g===null)&&s.append(u===!0?gs([f],v,n):u===null?f:f+"[]",l(g))}),!1}return Ht(y)?!0:(s.append(gs(_,f,n),l(y)),!1)}const I=[],B=Object.assign(yi,{defaultVisitor:d,convertValue:l,isVisitable:Ht});function T(y,f){if(!E.isUndefined(y)){if(I.indexOf(y)!==-1)throw Error("Circular reference detected in "+f.join("."));I.push(y),E.forEach(y,function(R,M){(!(E.isUndefined(R)||R===null)&&a.call(s,R,E.isString(M)?M.trim():M,f,B))===!0&&T(R,f?f.concat(M):[M])}),I.pop()}}if(!E.isObject(t))throw new TypeError("data must be an object");return T(t),s}function hs(t){const s={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(o){return s[o]})}function ts(t,s){this._pairs=[],t&&kt(t,this,s)}const Zs=ts.prototype;Zs.append=function(s,r){this._pairs.push([s,r])};Zs.toString=function(s){const r=s?function(o){return s.call(this,o,hs)}:hs;return this._pairs.map(function(a){return r(a[0])+"="+r(a[1])},"").join("&")};function bi(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function er(t,s,r){if(!s)return t;const o=r&&r.encode||bi;E.isFunction(r)&&(r={serialize:r});const a=r&&r.serialize;let n;if(a?n=a(s,r):n=E.isURLSearchParams(s)?s.toString():new ts(s,r).toString(o),n){const u=t.indexOf("#");u!==-1&&(t=t.slice(0,u)),t+=(t.indexOf("?")===-1?"?":"&")+n}return t}class xi{constructor(){this.handlers=[]}use(s,r,o){return this.handlers.push({fulfilled:s,rejected:r,synchronous:o?o.synchronous:!1,runWhen:o?o.runWhen:null}),this.handlers.length-1}eject(s){this.handlers[s]&&(this.handlers[s]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(s){E.forEach(this.handlers,function(o){o!==null&&s(o)})}}const ys=xi,tr={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},wi=typeof URLSearchParams<"u"?URLSearchParams:ts,_i=typeof FormData<"u"?FormData:null,ki=typeof Blob<"u"?Blob:null,Ci={isBrowser:!0,classes:{URLSearchParams:wi,FormData:_i,Blob:ki},protocols:["http","https","file","blob","url","data"]},ss=typeof window<"u"&&typeof document<"u",Wt=typeof navigator=="object"&&navigator||void 0,$i=ss&&(!Wt||["ReactNative","NativeScript","NS"].indexOf(Wt.product)<0),Si=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Mi=ss&&window.location.href||"http://localhost",Ai=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ss,hasStandardBrowserEnv:$i,hasStandardBrowserWebWorkerEnv:Si,navigator:Wt,origin:Mi},Symbol.toStringTag,{value:"Module"})),_e={...Ai,...Ci};function ji(t,s){return kt(t,new _e.classes.URLSearchParams,Object.assign({visitor:function(r,o,a,n){return _e.isNode&&E.isBuffer(r)?(this.append(o,r.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},s))}function Ti(t){return E.matchAll(/\w+|\[(\w*)]/g,t).map(s=>s[0]==="[]"?"":s[1]||s[0])}function Ii(t){const s={},r=Object.keys(t);let o;const a=r.length;let n;for(o=0;o<a;o++)n=r[o],s[n]=t[n];return s}function sr(t){function s(r,o,a,n){let u=r[n++];if(u==="__proto__")return!0;const b=Number.isFinite(+u),w=n>=r.length;return u=!u&&E.isArray(a)?a.length:u,w?(E.hasOwnProp(a,u)?a[u]=[a[u],o]:a[u]=o,!b):((!a[u]||!E.isObject(a[u]))&&(a[u]=[]),s(r,o,a[u],n)&&E.isArray(a[u])&&(a[u]=Ii(a[u])),!b)}if(E.isFormData(t)&&E.isFunction(t.entries)){const r={};return E.forEachEntry(t,(o,a)=>{s(Ti(o),a,r,0)}),r}return null}function Bi(t,s,r){if(E.isString(t))try{return(s||JSON.parse)(t),E.trim(t)}catch(o){if(o.name!=="SyntaxError")throw o}return(r||JSON.stringify)(t)}const rs={transitional:tr,adapter:["xhr","http","fetch"],transformRequest:[function(s,r){const o=r.getContentType()||"",a=o.indexOf("application/json")>-1,n=E.isObject(s);if(n&&E.isHTMLForm(s)&&(s=new FormData(s)),E.isFormData(s))return a?JSON.stringify(sr(s)):s;if(E.isArrayBuffer(s)||E.isBuffer(s)||E.isStream(s)||E.isFile(s)||E.isBlob(s)||E.isReadableStream(s))return s;if(E.isArrayBufferView(s))return s.buffer;if(E.isURLSearchParams(s))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),s.toString();let b;if(n){if(o.indexOf("application/x-www-form-urlencoded")>-1)return ji(s,this.formSerializer).toString();if((b=E.isFileList(s))||o.indexOf("multipart/form-data")>-1){const w=this.env&&this.env.FormData;return kt(b?{"files[]":s}:s,w&&new w,this.formSerializer)}}return n||a?(r.setContentType("application/json",!1),Bi(s)):s}],transformResponse:[function(s){const r=this.transitional||rs.transitional,o=r&&r.forcedJSONParsing,a=this.responseType==="json";if(E.isResponse(s)||E.isReadableStream(s))return s;if(s&&E.isString(s)&&(o&&!this.responseType||a)){const u=!(r&&r.silentJSONParsing)&&a;try{return JSON.parse(s)}catch(b){if(u)throw b.name==="SyntaxError"?ae.from(b,ae.ERR_BAD_RESPONSE,this,null,this.response):b}}return s}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:_e.classes.FormData,Blob:_e.classes.Blob},validateStatus:function(s){return s>=200&&s<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};E.forEach(["delete","get","head","post","put","patch"],t=>{rs.headers[t]={}});const os=rs,Ei=E.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ni=t=>{const s={};let r,o,a;return t&&t.split(`
`).forEach(function(u){a=u.indexOf(":"),r=u.substring(0,a).trim().toLowerCase(),o=u.substring(a+1).trim(),!(!r||s[r]&&Ei[r])&&(r==="set-cookie"?s[r]?s[r].push(o):s[r]=[o]:s[r]=s[r]?s[r]+", "+o:o)}),s},bs=Symbol("internals");function Ze(t){return t&&String(t).trim().toLowerCase()}function ct(t){return t===!1||t==null?t:E.isArray(t)?t.map(ct):String(t)}function Di(t){const s=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;for(;o=r.exec(t);)s[o[1]]=o[2];return s}const Pi=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function Bt(t,s,r,o,a){if(E.isFunction(o))return o.call(this,s,r);if(a&&(s=r),!!E.isString(s)){if(E.isString(o))return s.indexOf(o)!==-1;if(E.isRegExp(o))return o.test(s)}}function Ri(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(s,r,o)=>r.toUpperCase()+o)}function Li(t,s){const r=E.toCamelCase(" "+s);["get","set","has"].forEach(o=>{Object.defineProperty(t,o+r,{value:function(a,n,u){return this[o].call(this,s,a,n,u)},configurable:!0})})}class Ct{constructor(s){s&&this.set(s)}set(s,r,o){const a=this;function n(b,w,l){const d=Ze(w);if(!d)throw new Error("header name must be a non-empty string");const I=E.findKey(a,d);(!I||a[I]===void 0||l===!0||l===void 0&&a[I]!==!1)&&(a[I||w]=ct(b))}const u=(b,w)=>E.forEach(b,(l,d)=>n(l,d,w));if(E.isPlainObject(s)||s instanceof this.constructor)u(s,r);else if(E.isString(s)&&(s=s.trim())&&!Pi(s))u(Ni(s),r);else if(E.isObject(s)&&E.isIterable(s)){let b={},w,l;for(const d of s){if(!E.isArray(d))throw TypeError("Object iterator must return a key-value pair");b[l=d[0]]=(w=b[l])?E.isArray(w)?[...w,d[1]]:[w,d[1]]:d[1]}u(b,r)}else s!=null&&n(r,s,o);return this}get(s,r){if(s=Ze(s),s){const o=E.findKey(this,s);if(o){const a=this[o];if(!r)return a;if(r===!0)return Di(a);if(E.isFunction(r))return r.call(this,a,o);if(E.isRegExp(r))return r.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(s,r){if(s=Ze(s),s){const o=E.findKey(this,s);return!!(o&&this[o]!==void 0&&(!r||Bt(this,this[o],o,r)))}return!1}delete(s,r){const o=this;let a=!1;function n(u){if(u=Ze(u),u){const b=E.findKey(o,u);b&&(!r||Bt(o,o[b],b,r))&&(delete o[b],a=!0)}}return E.isArray(s)?s.forEach(n):n(s),a}clear(s){const r=Object.keys(this);let o=r.length,a=!1;for(;o--;){const n=r[o];(!s||Bt(this,this[n],n,s,!0))&&(delete this[n],a=!0)}return a}normalize(s){const r=this,o={};return E.forEach(this,(a,n)=>{const u=E.findKey(o,n);if(u){r[u]=ct(a),delete r[n];return}const b=s?Ri(n):String(n).trim();b!==n&&delete r[n],r[b]=ct(a),o[b]=!0}),this}concat(...s){return this.constructor.concat(this,...s)}toJSON(s){const r=Object.create(null);return E.forEach(this,(o,a)=>{o!=null&&o!==!1&&(r[a]=s&&E.isArray(o)?o.join(", "):o)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([s,r])=>s+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(s){return s instanceof this?s:new this(s)}static concat(s,...r){const o=new this(s);return r.forEach(a=>o.set(a)),o}static accessor(s){const o=(this[bs]=this[bs]={accessors:{}}).accessors,a=this.prototype;function n(u){const b=Ze(u);o[b]||(Li(a,u),o[b]=!0)}return E.isArray(s)?s.forEach(n):n(s),this}}Ct.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);E.reduceDescriptors(Ct.prototype,({value:t},s)=>{let r=s[0].toUpperCase()+s.slice(1);return{get:()=>t,set(o){this[r]=o}}});E.freezeMethods(Ct);const Ie=Ct;function Et(t,s){const r=this||os,o=s||r,a=Ie.from(o.headers);let n=o.data;return E.forEach(t,function(b){n=b.call(r,n,a.normalize(),s?s.status:void 0)}),a.normalize(),n}function rr(t){return!!(t&&t.__CANCEL__)}function Ge(t,s,r){ae.call(this,t??"canceled",ae.ERR_CANCELED,s,r),this.name="CanceledError"}E.inherits(Ge,ae,{__CANCEL__:!0});function or(t,s,r){const o=r.config.validateStatus;!r.status||!o||o(r.status)?t(r):s(new ae("Request failed with status code "+r.status,[ae.ERR_BAD_REQUEST,ae.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function zi(t){const s=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return s&&s[1]||""}function Oi(t,s){t=t||10;const r=new Array(t),o=new Array(t);let a=0,n=0,u;return s=s!==void 0?s:1e3,function(w){const l=Date.now(),d=o[n];u||(u=l),r[a]=w,o[a]=l;let I=n,B=0;for(;I!==a;)B+=r[I++],I=I%t;if(a=(a+1)%t,a===n&&(n=(n+1)%t),l-u<s)return;const T=d&&l-d;return T?Math.round(B*1e3/T):void 0}}function Fi(t,s){let r=0,o=1e3/s,a,n;const u=(l,d=Date.now())=>{r=d,a=null,n&&(clearTimeout(n),n=null),t.apply(null,l)};return[(...l)=>{const d=Date.now(),I=d-r;I>=o?u(l,d):(a=l,n||(n=setTimeout(()=>{n=null,u(a)},o-I)))},()=>a&&u(a)]}const vt=(t,s,r=3)=>{let o=0;const a=Oi(50,250);return Fi(n=>{const u=n.loaded,b=n.lengthComputable?n.total:void 0,w=u-o,l=a(w),d=u<=b;o=u;const I={loaded:u,total:b,progress:b?u/b:void 0,bytes:w,rate:l||void 0,estimated:l&&b&&d?(b-u)/l:void 0,event:n,lengthComputable:b!=null,[s?"download":"upload"]:!0};t(I)},r)},xs=(t,s)=>{const r=t!=null;return[o=>s[0]({lengthComputable:r,total:t,loaded:o}),s[1]]},ws=t=>(...s)=>E.asap(()=>t(...s)),Vi=_e.hasStandardBrowserEnv?((t,s)=>r=>(r=new URL(r,_e.origin),t.protocol===r.protocol&&t.host===r.host&&(s||t.port===r.port)))(new URL(_e.origin),_e.navigator&&/(msie|trident)/i.test(_e.navigator.userAgent)):()=>!0,qi=_e.hasStandardBrowserEnv?{write(t,s,r,o,a,n){const u=[t+"="+encodeURIComponent(s)];E.isNumber(r)&&u.push("expires="+new Date(r).toGMTString()),E.isString(o)&&u.push("path="+o),E.isString(a)&&u.push("domain="+a),n===!0&&u.push("secure"),document.cookie=u.join("; ")},read(t){const s=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return s?decodeURIComponent(s[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ui(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function Hi(t,s){return s?t.replace(/\/?\/$/,"")+"/"+s.replace(/^\/+/,""):t}function ar(t,s,r){let o=!Ui(s);return t&&(o||r==!1)?Hi(t,s):s}const _s=t=>t instanceof Ie?{...t}:t;function Ve(t,s){s=s||{};const r={};function o(l,d,I,B){return E.isPlainObject(l)&&E.isPlainObject(d)?E.merge.call({caseless:B},l,d):E.isPlainObject(d)?E.merge({},d):E.isArray(d)?d.slice():d}function a(l,d,I,B){if(E.isUndefined(d)){if(!E.isUndefined(l))return o(void 0,l,I,B)}else return o(l,d,I,B)}function n(l,d){if(!E.isUndefined(d))return o(void 0,d)}function u(l,d){if(E.isUndefined(d)){if(!E.isUndefined(l))return o(void 0,l)}else return o(void 0,d)}function b(l,d,I){if(I in s)return o(l,d);if(I in t)return o(void 0,l)}const w={url:n,method:n,data:n,baseURL:u,transformRequest:u,transformResponse:u,paramsSerializer:u,timeout:u,timeoutMessage:u,withCredentials:u,withXSRFToken:u,adapter:u,responseType:u,xsrfCookieName:u,xsrfHeaderName:u,onUploadProgress:u,onDownloadProgress:u,decompress:u,maxContentLength:u,maxBodyLength:u,beforeRedirect:u,transport:u,httpAgent:u,httpsAgent:u,cancelToken:u,socketPath:u,responseEncoding:u,validateStatus:b,headers:(l,d,I)=>a(_s(l),_s(d),I,!0)};return E.forEach(Object.keys(Object.assign({},t,s)),function(d){const I=w[d]||a,B=I(t[d],s[d],d);E.isUndefined(B)&&I!==b||(r[d]=B)}),r}const nr=t=>{const s=Ve({},t);let{data:r,withXSRFToken:o,xsrfHeaderName:a,xsrfCookieName:n,headers:u,auth:b}=s;s.headers=u=Ie.from(u),s.url=er(ar(s.baseURL,s.url,s.allowAbsoluteUrls),t.params,t.paramsSerializer),b&&u.set("Authorization","Basic "+btoa((b.username||"")+":"+(b.password?unescape(encodeURIComponent(b.password)):"")));let w;if(E.isFormData(r)){if(_e.hasStandardBrowserEnv||_e.hasStandardBrowserWebWorkerEnv)u.setContentType(void 0);else if((w=u.getContentType())!==!1){const[l,...d]=w?w.split(";").map(I=>I.trim()).filter(Boolean):[];u.setContentType([l||"multipart/form-data",...d].join("; "))}}if(_e.hasStandardBrowserEnv&&(o&&E.isFunction(o)&&(o=o(s)),o||o!==!1&&Vi(s.url))){const l=a&&n&&qi.read(n);l&&u.set(a,l)}return s},Wi=typeof XMLHttpRequest<"u",Ki=Wi&&function(t){return new Promise(function(r,o){const a=nr(t);let n=a.data;const u=Ie.from(a.headers).normalize();let{responseType:b,onUploadProgress:w,onDownloadProgress:l}=a,d,I,B,T,y;function f(){T&&T(),y&&y(),a.cancelToken&&a.cancelToken.unsubscribe(d),a.signal&&a.signal.removeEventListener("abort",d)}let _=new XMLHttpRequest;_.open(a.method.toUpperCase(),a.url,!0),_.timeout=a.timeout;function R(){if(!_)return;const g=Ie.from("getAllResponseHeaders"in _&&_.getAllResponseHeaders()),x={data:!b||b==="text"||b==="json"?_.responseText:_.response,status:_.status,statusText:_.statusText,headers:g,config:t,request:_};or(function(A){r(A),f()},function(A){o(A),f()},x),_=null}"onloadend"in _?_.onloadend=R:_.onreadystatechange=function(){!_||_.readyState!==4||_.status===0&&!(_.responseURL&&_.responseURL.indexOf("file:")===0)||setTimeout(R)},_.onabort=function(){_&&(o(new ae("Request aborted",ae.ECONNABORTED,t,_)),_=null)},_.onerror=function(){o(new ae("Network Error",ae.ERR_NETWORK,t,_)),_=null},_.ontimeout=function(){let v=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const x=a.transitional||tr;a.timeoutErrorMessage&&(v=a.timeoutErrorMessage),o(new ae(v,x.clarifyTimeoutError?ae.ETIMEDOUT:ae.ECONNABORTED,t,_)),_=null},n===void 0&&u.setContentType(null),"setRequestHeader"in _&&E.forEach(u.toJSON(),function(v,x){_.setRequestHeader(x,v)}),E.isUndefined(a.withCredentials)||(_.withCredentials=!!a.withCredentials),b&&b!=="json"&&(_.responseType=a.responseType),l&&([B,y]=vt(l,!0),_.addEventListener("progress",B)),w&&_.upload&&([I,T]=vt(w),_.upload.addEventListener("progress",I),_.upload.addEventListener("loadend",T)),(a.cancelToken||a.signal)&&(d=g=>{_&&(o(!g||g.type?new Ge(null,t,_):g),_.abort(),_=null)},a.cancelToken&&a.cancelToken.subscribe(d),a.signal&&(a.signal.aborted?d():a.signal.addEventListener("abort",d)));const M=zi(a.url);if(M&&_e.protocols.indexOf(M)===-1){o(new ae("Unsupported protocol "+M+":",ae.ERR_BAD_REQUEST,t));return}_.send(n||null)})},Yi=(t,s)=>{const{length:r}=t=t?t.filter(Boolean):[];if(s||r){let o=new AbortController,a;const n=function(l){if(!a){a=!0,b();const d=l instanceof Error?l:this.reason;o.abort(d instanceof ae?d:new Ge(d instanceof Error?d.message:d))}};let u=s&&setTimeout(()=>{u=null,n(new ae(`timeout ${s} of ms exceeded`,ae.ETIMEDOUT))},s);const b=()=>{t&&(u&&clearTimeout(u),u=null,t.forEach(l=>{l.unsubscribe?l.unsubscribe(n):l.removeEventListener("abort",n)}),t=null)};t.forEach(l=>l.addEventListener("abort",n));const{signal:w}=o;return w.unsubscribe=()=>E.asap(b),w}},Qi=Yi,Xi=function*(t,s){let r=t.byteLength;if(!s||r<s){yield t;return}let o=0,a;for(;o<r;)a=o+s,yield t.slice(o,a),o=a},Gi=async function*(t,s){for await(const r of Ji(t))yield*Xi(r,s)},Ji=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const s=t.getReader();try{for(;;){const{done:r,value:o}=await s.read();if(r)break;yield o}}finally{await s.cancel()}},ks=(t,s,r,o)=>{const a=Gi(t,s);let n=0,u,b=w=>{u||(u=!0,o&&o(w))};return new ReadableStream({async pull(w){try{const{done:l,value:d}=await a.next();if(l){b(),w.close();return}let I=d.byteLength;if(r){let B=n+=I;r(B)}w.enqueue(new Uint8Array(d))}catch(l){throw b(l),l}},cancel(w){return b(w),a.return()}},{highWaterMark:2})},$t=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ir=$t&&typeof ReadableStream=="function",Zi=$t&&(typeof TextEncoder=="function"?(t=>s=>t.encode(s))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),lr=(t,...s)=>{try{return!!t(...s)}catch{return!1}},el=ir&&lr(()=>{let t=!1;const s=new Request(_e.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!s}),Cs=64*1024,Kt=ir&&lr(()=>E.isReadableStream(new Response("").body)),gt={stream:Kt&&(t=>t.body)};$t&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(s=>{!gt[s]&&(gt[s]=E.isFunction(t[s])?r=>r[s]():(r,o)=>{throw new ae(`Response type '${s}' is not supported`,ae.ERR_NOT_SUPPORT,o)})})})(new Response);const tl=async t=>{if(t==null)return 0;if(E.isBlob(t))return t.size;if(E.isSpecCompliantForm(t))return(await new Request(_e.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(E.isArrayBufferView(t)||E.isArrayBuffer(t))return t.byteLength;if(E.isURLSearchParams(t)&&(t=t+""),E.isString(t))return(await Zi(t)).byteLength},sl=async(t,s)=>{const r=E.toFiniteNumber(t.getContentLength());return r??tl(s)},rl=$t&&(async t=>{let{url:s,method:r,data:o,signal:a,cancelToken:n,timeout:u,onDownloadProgress:b,onUploadProgress:w,responseType:l,headers:d,withCredentials:I="same-origin",fetchOptions:B}=nr(t);l=l?(l+"").toLowerCase():"text";let T=Qi([a,n&&n.toAbortSignal()],u),y;const f=T&&T.unsubscribe&&(()=>{T.unsubscribe()});let _;try{if(w&&el&&r!=="get"&&r!=="head"&&(_=await sl(d,o))!==0){let x=new Request(s,{method:"POST",body:o,duplex:"half"}),j;if(E.isFormData(o)&&(j=x.headers.get("content-type"))&&d.setContentType(j),x.body){const[A,O]=xs(_,vt(ws(w)));o=ks(x.body,Cs,A,O)}}E.isString(I)||(I=I?"include":"omit");const R="credentials"in Request.prototype;y=new Request(s,{...B,signal:T,method:r.toUpperCase(),headers:d.normalize().toJSON(),body:o,duplex:"half",credentials:R?I:void 0});let M=await fetch(y,B);const g=Kt&&(l==="stream"||l==="response");if(Kt&&(b||g&&f)){const x={};["status","statusText","headers"].forEach(Z=>{x[Z]=M[Z]});const j=E.toFiniteNumber(M.headers.get("content-length")),[A,O]=b&&xs(j,vt(ws(b),!0))||[];M=new Response(ks(M.body,Cs,A,()=>{O&&O(),f&&f()}),x)}l=l||"text";let v=await gt[E.findKey(gt,l)||"text"](M,t);return!g&&f&&f(),await new Promise((x,j)=>{or(x,j,{data:v,headers:Ie.from(M.headers),status:M.status,statusText:M.statusText,config:t,request:y})})}catch(R){throw f&&f(),R&&R.name==="TypeError"&&/Load failed|fetch/i.test(R.message)?Object.assign(new ae("Network Error",ae.ERR_NETWORK,t,y),{cause:R.cause||R}):ae.from(R,R&&R.code,t,y)}}),Yt={http:gi,xhr:Ki,fetch:rl};E.forEach(Yt,(t,s)=>{if(t){try{Object.defineProperty(t,"name",{value:s})}catch{}Object.defineProperty(t,"adapterName",{value:s})}});const $s=t=>`- ${t}`,ol=t=>E.isFunction(t)||t===null||t===!1,dr={getAdapter:t=>{t=E.isArray(t)?t:[t];const{length:s}=t;let r,o;const a={};for(let n=0;n<s;n++){r=t[n];let u;if(o=r,!ol(r)&&(o=Yt[(u=String(r)).toLowerCase()],o===void 0))throw new ae(`Unknown adapter '${u}'`);if(o)break;a[u||"#"+n]=o}if(!o){const n=Object.entries(a).map(([b,w])=>`adapter ${b} `+(w===!1?"is not supported by the environment":"is not available in the build"));let u=s?n.length>1?`since :
`+n.map($s).join(`
`):" "+$s(n[0]):"as no adapter specified";throw new ae("There is no suitable adapter to dispatch the request "+u,"ERR_NOT_SUPPORT")}return o},adapters:Yt};function Nt(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new Ge(null,t)}function Ss(t){return Nt(t),t.headers=Ie.from(t.headers),t.data=Et.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),dr.getAdapter(t.adapter||os.adapter)(t).then(function(o){return Nt(t),o.data=Et.call(t,t.transformResponse,o),o.headers=Ie.from(o.headers),o},function(o){return rr(o)||(Nt(t),o&&o.response&&(o.response.data=Et.call(t,t.transformResponse,o.response),o.response.headers=Ie.from(o.response.headers))),Promise.reject(o)})}const ur="1.10.0",St={};["object","boolean","number","function","string","symbol"].forEach((t,s)=>{St[t]=function(o){return typeof o===t||"a"+(s<1?"n ":" ")+t}});const Ms={};St.transitional=function(s,r,o){function a(n,u){return"[Axios v"+ur+"] Transitional option '"+n+"'"+u+(o?". "+o:"")}return(n,u,b)=>{if(s===!1)throw new ae(a(u," has been removed"+(r?" in "+r:"")),ae.ERR_DEPRECATED);return r&&!Ms[u]&&(Ms[u]=!0,console.warn(a(u," has been deprecated since v"+r+" and will be removed in the near future"))),s?s(n,u,b):!0}};St.spelling=function(s){return(r,o)=>(console.warn(`${o} is likely a misspelling of ${s}`),!0)};function al(t,s,r){if(typeof t!="object")throw new ae("options must be an object",ae.ERR_BAD_OPTION_VALUE);const o=Object.keys(t);let a=o.length;for(;a-- >0;){const n=o[a],u=s[n];if(u){const b=t[n],w=b===void 0||u(b,n,t);if(w!==!0)throw new ae("option "+n+" must be "+w,ae.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new ae("Unknown option "+n,ae.ERR_BAD_OPTION)}}const mt={assertOptions:al,validators:St},De=mt.validators;class ht{constructor(s){this.defaults=s||{},this.interceptors={request:new ys,response:new ys}}async request(s,r){try{return await this._request(s,r)}catch(o){if(o instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const n=a.stack?a.stack.replace(/^.+\n/,""):"";try{o.stack?n&&!String(o.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(o.stack+=`
`+n):o.stack=n}catch{}}throw o}}_request(s,r){typeof s=="string"?(r=r||{},r.url=s):r=s||{},r=Ve(this.defaults,r);const{transitional:o,paramsSerializer:a,headers:n}=r;o!==void 0&&mt.assertOptions(o,{silentJSONParsing:De.transitional(De.boolean),forcedJSONParsing:De.transitional(De.boolean),clarifyTimeoutError:De.transitional(De.boolean)},!1),a!=null&&(E.isFunction(a)?r.paramsSerializer={serialize:a}:mt.assertOptions(a,{encode:De.function,serialize:De.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),mt.assertOptions(r,{baseUrl:De.spelling("baseURL"),withXsrfToken:De.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let u=n&&E.merge(n.common,n[r.method]);n&&E.forEach(["delete","get","head","post","put","patch","common"],y=>{delete n[y]}),r.headers=Ie.concat(u,n);const b=[];let w=!0;this.interceptors.request.forEach(function(f){typeof f.runWhen=="function"&&f.runWhen(r)===!1||(w=w&&f.synchronous,b.unshift(f.fulfilled,f.rejected))});const l=[];this.interceptors.response.forEach(function(f){l.push(f.fulfilled,f.rejected)});let d,I=0,B;if(!w){const y=[Ss.bind(this),void 0];for(y.unshift.apply(y,b),y.push.apply(y,l),B=y.length,d=Promise.resolve(r);I<B;)d=d.then(y[I++],y[I++]);return d}B=b.length;let T=r;for(I=0;I<B;){const y=b[I++],f=b[I++];try{T=y(T)}catch(_){f.call(this,_);break}}try{d=Ss.call(this,T)}catch(y){return Promise.reject(y)}for(I=0,B=l.length;I<B;)d=d.then(l[I++],l[I++]);return d}getUri(s){s=Ve(this.defaults,s);const r=ar(s.baseURL,s.url,s.allowAbsoluteUrls);return er(r,s.params,s.paramsSerializer)}}E.forEach(["delete","get","head","options"],function(s){ht.prototype[s]=function(r,o){return this.request(Ve(o||{},{method:s,url:r,data:(o||{}).data}))}});E.forEach(["post","put","patch"],function(s){function r(o){return function(n,u,b){return this.request(Ve(b||{},{method:s,headers:o?{"Content-Type":"multipart/form-data"}:{},url:n,data:u}))}}ht.prototype[s]=r(),ht.prototype[s+"Form"]=r(!0)});const ft=ht;class as{constructor(s){if(typeof s!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(n){r=n});const o=this;this.promise.then(a=>{if(!o._listeners)return;let n=o._listeners.length;for(;n-- >0;)o._listeners[n](a);o._listeners=null}),this.promise.then=a=>{let n;const u=new Promise(b=>{o.subscribe(b),n=b}).then(a);return u.cancel=function(){o.unsubscribe(n)},u},s(function(n,u,b){o.reason||(o.reason=new Ge(n,u,b),r(o.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(s){if(this.reason){s(this.reason);return}this._listeners?this._listeners.push(s):this._listeners=[s]}unsubscribe(s){if(!this._listeners)return;const r=this._listeners.indexOf(s);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const s=new AbortController,r=o=>{s.abort(o)};return this.subscribe(r),s.signal.unsubscribe=()=>this.unsubscribe(r),s.signal}static source(){let s;return{token:new as(function(a){s=a}),cancel:s}}}const nl=as;function il(t){return function(r){return t.apply(null,r)}}function ll(t){return E.isObject(t)&&t.isAxiosError===!0}const Qt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Qt).forEach(([t,s])=>{Qt[s]=t});const dl=Qt;function cr(t){const s=new ft(t),r=Vs(ft.prototype.request,s);return E.extend(r,ft.prototype,s,{allOwnKeys:!0}),E.extend(r,s,null,{allOwnKeys:!0}),r.create=function(a){return cr(Ve(t,a))},r}const ye=cr(os);ye.Axios=ft;ye.CanceledError=Ge;ye.CancelToken=nl;ye.isCancel=rr;ye.VERSION=ur;ye.toFormData=kt;ye.AxiosError=ae;ye.Cancel=ye.CanceledError;ye.all=function(s){return Promise.all(s)};ye.spread=il;ye.isAxiosError=ll;ye.mergeConfig=Ve;ye.AxiosHeaders=Ie;ye.formToJSON=t=>sr(E.isHTMLForm(t)?new FormData(t):t);ye.getAdapter=dr.getAdapter;ye.HttpStatusCode=dl;ye.default=ye;const ke=ye,ns="vertigo_cart_state",ul="vertigo_cart_sync",mr=t=>({id:t.id,name:t.name,title:t.title,description:t.description,target_amount:t.target_amount,bid_amount:t.bid_amount,auction_id:t.auction_id,auction_type_id:t.auction_type_id,status:t.status,type:t.type,cropped:t.cropped,image:t.image,code:t.code,reference_number:t.reference_number,date_from:t.date_from,date_to:t.date_to,closed_by:t.closed_by,branch_id:t.branch_id,created_at:t.created_at,updated_at:t.updated_at,quantity:t.quantity,optimistic:t.optimistic,lastUpdated:t.lastUpdated}),We=(t,s)=>{try{const r={items:t.map(mr),lastSyncTime:s,version:"2.0"};localStorage.setItem(ns,JSON.stringify(r))}catch(r){console.warn("Failed to save cart to localStorage:",r)}},cl=()=>{try{const t=localStorage.getItem(ns);if(t){const s=JSON.parse(t);if(s.version==="2.0")return s}}catch(t){console.warn("Failed to load cart from localStorage:",t)}return null},Dt=()=>{try{localStorage.removeItem(ns)}catch(t){console.warn("Failed to clear cart storage:",t)}},Le=at("cart",()=>{const t=P([]),s=P(!1),r=P(null),o=P(Date.now()),a=P(0),n=P(navigator.onLine);let u=null;typeof BroadcastChannel<"u"&&(u=new BroadcastChannel(ul),u.onmessage=F=>{F.data.type==="CART_UPDATED"&&F.data.timestamp>o.value&&(t.value=F.data.items,o.value=F.data.timestamp,l())});const b=()=>{n.value=navigator.onLine,n.value&&a.value>0&&d()};window.addEventListener("online",b),window.addEventListener("offline",b),Me(t,F=>{Y(F,o.value),w(F)},{deep:!0});const w=F=>{if(u){const U=Date.now();o.value=U;const Q=F.map(mr);u.postMessage({type:"CART_UPDATED",items:Q,timestamp:U})}},l=()=>{window.dispatchEvent(new CustomEvent("cart-updated",{detail:{count:t.value.length}}))},d=async()=>{if(n.value)try{await O(),a.value=0}catch{a.value++,console.warn("Cart sync failed, will retry when online")}},I=q(()=>t.value.length),B=q(()=>t.value.reduce((F,U)=>{const Q=U.target_amount||0,ee=U.quantity||1;return F+Q*ee},0)),T=q(()=>t.value.length===0),y=q(()=>t.value.filter(F=>{var U;return((U=F.auction_type)==null?void 0:U.type)==="cash"})),f=q(()=>t.value.filter(F=>{var U;return((U=F.auction_type)==null?void 0:U.type)==="online"})),_=q(()=>t.value.filter(F=>{var U;return((U=F.auction_type)==null?void 0:U.type)==="live"})),R=async(F,U=!1)=>{s.value=!0,r.value=null;try{if(U)return window.location.href=`/add-to-cart/${F.id}?checkout=true`,!0;if(j(F.id))return r.value="Item is already in cart",!1;const Q={...F,quantity:1,optimistic:!0,lastUpdated:Date.now()};if(t.value.push(Q),l(),!n.value)return a.value++,!0;const ee=await ke.post(`/api/cart/add/${F.id}`,{quantity:1,source:"frontend"});if(ee.data.success){const ue=t.value.findIndex(xe=>xe.id===F.id);return ue>-1&&(t.value[ue].optimistic=!1),We(t.value,o.value),l(),!0}else{const ue=t.value.findIndex(xe=>xe.id===F.id&&xe.optimistic);return ue>-1&&(t.value.splice(ue,1),l()),r.value=ee.data.message||"Failed to add item to cart",!1}}catch(Q){const ee=t.value.findIndex(ue=>ue.id===F.id&&ue.optimistic);return ee>-1&&(t.value.splice(ee,1),l()),n.value?r.value="Failed to add item to cart":(r.value="No internet connection. Item will be added when connection is restored.",a.value++),console.error("Error adding to cart:",Q),!1}finally{s.value=!1}},M=async F=>{s.value=!0,r.value=null;try{const U=t.value.findIndex(ue=>ue.id===F.id);if(U===-1)return r.value="Item not found in cart",!1;const Q={...t.value[U]};if(t.value.splice(U,1),l(),!n.value)return a.value++,!0;const ee=await ke.delete(`/api/cart/remove/${F.id}`);return ee.data.success?(We(t.value,o.value),l(),!0):(t.value.splice(U,0,Q),l(),r.value=ee.data.message||"Failed to remove item from cart",!1)}catch(U){if(t.value.findIndex(ee=>ee.id===F.id)===-1){const ee={...F,quantity:1};t.value.push(ee),l()}return n.value?r.value="Failed to remove item from cart":(r.value="No internet connection. Item will be removed when connection is restored.",a.value++),console.error("Error removing from cart:",U),!1}finally{s.value=!1}},g=async(F,U)=>{const Q=t.value.find(ue=>ue.id===F);if(!Q)return r.value="Item not found in cart",!1;if(U<=0)return await M(Q);const ee=Q.quantity||1;if(Q.quantity=U,Q.lastUpdated=Date.now(),l(),!n.value)return a.value++,!0;try{const ue=await ke.put(`/api/cart/update/${F}`,{quantity:U});return ue.data.success?(We(t.value,o.value),l(),!0):(Q.quantity=ee,l(),r.value=ue.data.message||"Failed to update item quantity",!1)}catch(ue){return Q.quantity=ee,l(),n.value?r.value="Failed to update item quantity":(r.value="No internet connection. Quantity will be updated when connection is restored.",a.value++),console.error("Error updating quantity:",ue),!1}},v=async(F=!0)=>{try{return F&&n.value&&!(await ke.delete("/api/cart/clear")).data.success?(r.value="Failed to clear cart on server",!1):(t.value=[],r.value=null,Dt(),l(),!0)}catch(U){return n.value?(r.value="Failed to clear cart",console.error("Error clearing cart:",U),!1):(t.value=[],Dt(),l(),a.value++,!0)}},x=async()=>{const F=[];if(t.value.length===0)return{valid:!0,issues:[]};try{const U=t.value.map(ue=>ue.id),Q=await ke.post("/api/cart/validate",{item_ids:U});Q.data.issues&&F.push(...Q.data.issues);const ee=Q.data.unavailable_items||[];return ee.length>0&&(t.value=t.value.filter(ue=>!ee.includes(ue.id)),l(),F.push(`${ee.length} item(s) are no longer available and have been removed from your cart.`)),{valid:F.length===0,issues:F}}catch(U){return console.error("Cart validation error:",U),{valid:!0,issues:[]}}},j=F=>t.value.some(U=>U.id===F),A=F=>{if(F&&F.length>0)t.value=F.map(U=>({...U,quantity:U.quantity||1,lastUpdated:Date.now()})),o.value=Date.now(),We(t.value,o.value);else{const U=cl();U&&U.items.length>0&&(t.value=U.items,o.value=U.lastSyncTime,n.value&&x().then(({valid:Q,issues:ee})=>{!Q&&ee.length>0&&console.warn("Cart validation issues:",ee)}))}l()},O=async(F=!0)=>{if(!n.value&&F)return console.warn("Cannot fetch cart while offline"),!1;s.value=!0,r.value=null;try{const U=await ke.get("/api/cart");return U.data.success&&U.data.items?(t.value=U.data.items.map(Q=>({id:Q.id,name:Q.name,target_amount:Q.target_amount,quantity:Q.quantity||1,auction_type:Q.auction_type,image:Q.image,cropped:Q.image,lastUpdated:Date.now()})),o.value=Date.now(),We(t.value,o.value)):(t.value=[],Dt()),l(),a.value=0,!0}catch(U){return r.value="Failed to fetch cart",console.error("Error fetching cart:",U),F&&a.value<3&&(a.value++,setTimeout(()=>{O(!1)},Math.pow(2,a.value)*1e3)),!1}finally{s.value=!1}},Z=F=>!F||F<=0?"MK 0":`MK ${F.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,oe=F=>{const U=F.target_amount||0,Q=F.quantity||1;return U*Q},de=()=>{t.value=[],s.value=!1,r.value=null},te=(F,U={})=>{try{const Q={event:F,timestamp:Date.now(),cart_count:t.value.length,cart_total:B.value,...U};typeof gtag<"u"&&gtag("event",F,{event_category:"cart",event_label:U.item_name||"",value:U.item_price||0});const ee=JSON.parse(localStorage.getItem("cart_analytics")||"[]");ee.push(Q),ee.length>100&&ee.splice(0,ee.length-100),localStorage.setItem("cart_analytics",JSON.stringify(ee))}catch(Q){console.warn("Failed to track cart event:",Q)}};let V=null;const Y=(F,U)=>{V&&clearTimeout(V),V=setTimeout(()=>{We(F,U)},300)};return{items:t,loading:s,error:r,lastSyncTime:o,retryCount:a,isOnline:n,cartCount:I,cartTotal:B,isEmpty:T,cashItems:y,onlineItems:f,liveItems:_,addToCart:R,removeFromCart:M,updateQuantity:g,clearCart:v,isInCart:j,initializeCart:A,fetchCart:O,validateCartItems:x,syncWithServer:d,formatCurrency:Z,getItemSubtotal:oe,reset:de,setLastShoppingPage:F=>{try{!F.includes("/cart")&&!F.includes("/checkout")&&sessionStorage.setItem("lastShoppingPage",F)}catch(U){console.warn("Could not save last shopping page:",U)}},getLastShoppingPage:()=>{try{return sessionStorage.getItem("lastShoppingPage")}catch(F){return console.warn("Could not retrieve last shopping page:",F),null}},trackCartEvent:te,cleanup:()=>{u&&u.close(),window.removeEventListener("online",b),window.removeEventListener("offline",b),V&&clearTimeout(V)}}});function fr(){const t=ge(),s=qe(),r=P(!1),o=P("login"),a=q(()=>t.isAuthenticated),n=q(()=>t.user),u=q(()=>t.isLoading),b=async x=>{try{return await t.login(x)}catch(j){throw console.error("Login failed:",j),j}},w=async()=>{try{await t.logout(),s.push("/")}catch(x){throw console.error("Logout failed:",x),x}},l=async x=>{try{const j=await fetch("/api/register",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(x)});if(!j.ok){const O=await j.json();throw new Error(O.message||"Registration failed")}const A=await j.json();return await b({email:x.email,password:x.password,remember:!1}),A}catch(j){throw console.error("Registration failed:",j),j}},d=x=>a.value?(x==null||x(),!0):(B(),!1),I=async x=>a.value?(await(x==null?void 0:x()),!0):(B(),!1),B=()=>{o.value="login",r.value=!0},T=()=>{o.value="register",r.value=!0},y=()=>{r.value=!1},f=x=>{var j;return n.value&&((j=n.value.roles)==null?void 0:j.some(A=>A.name===x))||!1},_=x=>{var j;return n.value&&((j=n.value.permissions)==null?void 0:j.includes(x))||!1};return{isAuthenticated:a,user:n,isLoading:u,showAuthModal:r,authModalTab:o,login:b,logout:w,register:l,initialize:async()=>{await t.initialize()},requireAuth:d,requireAuthAsync:I,showLoginModal:B,showRegisterModal:T,hideAuthModal:y,hasRole:f,hasPermission:_,canPlaceBids:()=>a.value&&_("place_bids"),redirectToLogin:x=>{const j=x?{redirect:x}:{};s.push({name:"login",query:j})},redirectIfAuthenticated:(x="/")=>{a.value&&s.push(x)}}}const ml={class:"group relative bg-white rounded-xl border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1"},fl={class:"relative aspect-[4/3] overflow-hidden bg-gray-100"},pl=["src","alt"],vl={key:1,class:"w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200"},gl={class:"absolute top-3 left-3"},hl={class:"flex items-center space-x-1"},yl={class:"text-xs font-semibold"},bl={key:2,class:"absolute top-3 right-3"},xl={class:"text-xs font-medium"},wl={class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100"},_l={class:"p-5"},kl={class:"mb-3"},Cl={class:"font-bold text-gray-900 text-lg line-clamp-2 mb-1"},$l={key:0,class:"text-xs text-gray-500 font-mono"},Sl={class:"mb-4"},Ml={class:"flex items-baseline justify-between mb-1"},Al={class:"text-sm font-medium text-gray-600"},jl={key:0,class:"text-xs text-gray-500"},Tl={class:"flex items-baseline space-x-2"},Il={class:"text-2xl font-bold text-gray-900"},Bl={key:0,class:"text-sm text-gray-500 line-through"},El={key:0,class:"mb-4 p-3 bg-gray-50 rounded-lg"},Nl={class:"flex items-center justify-between text-sm"},Dl={class:"text-gray-600"},Pl={class:"font-medium text-gray-900"},Rl={key:0,class:"mt-2"},Ll={class:"w-full bg-gray-200 rounded-full h-1.5"},zl={key:1,class:"space-y-2"},Ol={key:0,class:"w-4 h-4 mr-2 animate-spin",fill:"none",viewBox:"0 0 24 24"},Fl={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Vl={key:0,class:"w-4 h-4 mr-2 animate-spin",fill:"none",viewBox:"0 0 24 24"},ql={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ul={key:2,class:"space-y-2"},Hl=["fill"],Wl=pe({__name:"ItemCard",props:{item:{}},emits:["view-details","place-bid","preview","add-to-cart","remove-from-cart","checkout","watch-item"],setup(t,{emit:s}){const r=t,o=s,a=Le(),n=nt(),u=Be(),{isAuthenticated:b,user:w,initialize:l}=fr(),d=P(!1),I=P(!1),B=P(!1),T=()=>He("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[He("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})]),y=()=>He("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[He("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})]),f=()=>He("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[He("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})]),_=C=>{switch(C){case"cash":return"Daily Sale";case"online":return"Online";case"live":return"Live";default:return"Auction"}},R=C=>{switch(C){case"cash":return T;case"online":return y;case"live":return f;default:return y}},M=C=>{const k="px-2 py-1 rounded-full text-white shadow-sm";switch(C){case"cash":return`${k} bg-green-500`;case"online":return`${k} bg-blue-500`;case"live":return`${k} bg-orange-500`;default:return`${k} bg-gray-500`}},g=C=>{var k,X;return C.closed_by?"SOLD":((k=C.auction_type)==null?void 0:k.type)==="live"&&ne(C)?"LIVE":((X=C.auction_type)==null?void 0:X.type)!=="cash"&&L(C)?"ENDING SOON":null},v=C=>{const k=g(C),X="px-2 py-1 rounded-full text-white shadow-sm";switch(k){case"SOLD":return`${X} bg-gray-600`;case"LIVE":return`${X} bg-red-500 animate-pulse`;case"ENDING SOON":return`${X} bg-yellow-500`;default:return`${X} bg-blue-500`}},x=C=>{switch(C){case"cash":return"Price";case"online":case"live":return"Current Bid";default:return"Price"}},j=C=>{var k;switch((k=C.auction_type)==null?void 0:k.type){case"cash":return C.target_amount||0;case"online":case"live":return C.bid_amount||C.target_amount||0;default:return C.target_amount||0}},A=C=>{var k;return((k=C.auction_type)==null?void 0:k.type)!=="cash"&&C.bid_amount&&C.target_amount&&C.bid_amount>C.target_amount?C.target_amount:null},O=C=>!C||C<=0?"0":C.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,"),Z=C=>{var k;return((k=C.auction_type)==null?void 0:k.type)!=="cash"&&(C.bid_amount||0)>0},oe=C=>Math.floor(Math.random()*10)+1+"",de=C=>!!(C.date_from||C.date_to),te=C=>{switch(C){case"cash":return"Available";case"online":return"Ends";case"live":return"Auction Date";default:return"Date"}},V=C=>{var k;if(((k=C.auction_type)==null?void 0:k.type)==="cash")return"Now";if(C.date_to){const X=new Date(C.date_to),m=new Date,i=X.getTime()-m.getTime();if(i<0)return"Ended";const K=Math.floor(i/(1e3*60*60*24)),N=Math.floor(i%(1e3*60*60*24)/(1e3*60*60));return K>0?`${K}d ${N}h`:N>0?`${N}h`:"Soon"}return"TBD"},Y=C=>{if(!C.date_from||!C.date_to)return"0%";const k=new Date(C.date_from).getTime(),X=new Date(C.date_to).getTime(),m=new Date().getTime();if(m<k)return"0%";if(m>X)return"100%";const i=(m-k)/(X-k)*100;return`${Math.min(100,Math.max(0,i))}%`},ne=C=>{if(!C.date_from||!C.date_to)return!1;const k=new Date;return k>=new Date(C.date_from)&&k<=new Date(C.date_to)},L=C=>{if(!C.date_to)return!1;const k=new Date(C.date_to),X=new Date,m=k.getTime()-X.getTime();return m>0&&m<24*60*60*1e3},$=async C=>{d.value=!0;try{o("add-to-cart",C)}finally{setTimeout(()=>{d.value=!1},500)}},F=async C=>{d.value=!0;try{o("remove-from-cart",C)}finally{setTimeout(()=>{d.value=!1},500)}},U=C=>{o("checkout",C)},Q=C=>{o("place-bid",C)},ee=async C=>{if(!b.value){B.value=!0;return}I.value=!0;try{if(await n.toggleWatchlist(C)){const X=Ne.value?"added to":"removed from";u.success(`${C.name} ${X} watchlist`)}else u.error(n.error||"Failed to update watchlist")}catch(k){console.error("Watchlist error:",k),u.error("Failed to update watchlist")}finally{I.value=!1}o("watch-item",C)},ue=async C=>{B.value=!1,u.success("You are now signed in and can add items to your watchlist!","Welcome!"),await n.initializeWatchlist(),await ee(r.item)},xe=C=>!!(C.closed_by||C.status==="sold"),Re=q(()=>a.isInCart(r.item.id)),Ne=q(()=>n.isInWatchlist(r.item.id));Te(async()=>{await l(),b.value&&n.items.length===0&&await n.syncWatchlistCount()});const je=C=>C.date_to?new Date>new Date(C.date_to):!1,Oe=C=>{var k;return je(C)?"Auction Ended":((k=C.auction_type)==null?void 0:k.type)==="live"&&ne(C)?"Bid Live":"Place Bid"};return(C,k)=>{var X,m,i,K,N,G,ie;return c(),p("div",ml,[e("div",fl,[C.item.cropped||C.item.image?(c(),p("img",{key:0,src:C.item.cropped||C.item.image,alt:C.item.name,class:"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110 cursor-pointer",onClick:k[0]||(k[0]=le=>C.$emit("preview",C.item))},null,8,pl)):(c(),p("div",vl,k[8]||(k[8]=[e("div",{class:"text-center"},[e("svg",{class:"w-16 h-16 text-gray-400 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("p",{class:"text-xs text-gray-500"},"No Image")],-1)]))),e("div",gl,[e("div",{class:J(M((X=C.item.auction_type)==null?void 0:X.type))},[e("div",hl,[(c(),ce(ot(R((m=C.item.auction_type)==null?void 0:m.type)),{class:"w-3 h-3"})),e("span",yl,S(_((i=C.item.auction_type)==null?void 0:i.type)),1)])],2)]),g(C.item)?(c(),p("div",bl,[e("div",{class:J(v(C.item))},[e("span",xl,S(g(C.item)),1)],2)])):z("",!0),e("div",wl,[e("button",{onClick:k[1]||(k[1]=le=>C.$emit("preview",C.item)),class:"bg-white text-gray-900 px-4 py-2 rounded-lg font-medium transform translate-y-2 group-hover:translate-y-0 transition-all duration-300 shadow-lg hover:shadow-xl"}," Quick View ")])]),e("div",_l,[e("div",kl,[e("h3",Cl,S(C.item.name),1),C.item.code||C.item.reference_number?(c(),p("div",$l," Ref: "+S(C.item.code||C.item.reference_number),1)):z("",!0)]),e("div",Sl,[e("div",Ml,[e("span",Al,S(x((K=C.item.auction_type)==null?void 0:K.type)),1),Z(C.item)?(c(),p("span",jl,S(oe(C.item))+" bids",1)):z("",!0)]),e("div",Tl,[e("span",Il,[k[9]||(k[9]=e("span",{class:"text-sm font-normal text-gray-500 mr-1"},"MK",-1)),W(S(O(j(C.item))),1)]),A(C.item)?(c(),p("span",Bl,[k[10]||(k[10]=e("span",{class:"text-xs font-normal text-gray-400 mr-1"},"MK",-1)),W(S(O(A(C.item))),1)])):z("",!0)])]),de(C.item)?(c(),p("div",El,[e("div",Nl,[e("span",Dl,S(te((N=C.item.auction_type)==null?void 0:N.type)),1),e("span",Pl,S(V(C.item)),1)]),((G=C.item.auction_type)==null?void 0:G.type)!=="cash"?(c(),p("div",Rl,[e("div",Ll,[e("div",{class:"bg-blue-600 h-1.5 rounded-full transition-all duration-300",style:rt({width:Y(C.item)})},null,4)])])):z("",!0)])):z("",!0),((ie=C.item.auction_type)==null?void 0:ie.type)==="cash"?(c(),p("div",zl,[Re.value?(c(),ce(h(se),{key:1,variant:"outline",class:"w-full font-semibold border-red-600 text-red-600 hover:bg-red-50 hover:border-red-700",onClick:k[3]||(k[3]=Ce(le=>F(C.item),["stop"])),disabled:d.value},{default:H(()=>[d.value?(c(),p("svg",Vl,k[13]||(k[13]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(c(),p("svg",ql,k[14]||(k[14]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"},null,-1)]))),W(" "+S(d.value?"Removing...":"Remove"),1)]),_:1},8,["disabled"])):(c(),ce(h(se),{key:0,variant:"outline",class:"w-full font-semibold border-blue-600 text-blue-600 hover:bg-blue-50 hover:border-blue-700",onClick:k[2]||(k[2]=Ce(le=>$(C.item),["stop"])),disabled:xe(C.item)||d.value},{default:H(()=>[d.value?(c(),p("svg",Ol,k[11]||(k[11]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(c(),p("svg",Fl,k[12]||(k[12]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"},null,-1)]))),W(" "+S(xe(C.item)?"Sold Out":d.value?"Adding...":"Add to Cart"),1)]),_:1},8,["disabled"])),D(h(se),{variant:"primary",class:"w-full font-semibold bg-green-600 hover:bg-green-700 border-green-600",onClick:k[4]||(k[4]=Ce(le=>U(C.item),["stop"])),disabled:xe(C.item)},{default:H(()=>[k[15]||(k[15]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),W(" "+S(xe(C.item)?"Unavailable":"Buy Now"),1)]),_:1,__:[15]},8,["disabled"])])):(c(),p("div",Ul,[D(h(se),{variant:"primary",class:"w-full font-semibold bg-blue-600 hover:bg-blue-700 border-blue-600",onClick:k[5]||(k[5]=Ce(le=>Q(C.item),["stop"])),disabled:je(C.item)},{default:H(()=>[k[16]||(k[16]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})],-1)),W(" "+S(Oe(C.item)),1)]),_:1,__:[16]},8,["disabled"]),D(h(se),{variant:"outline",class:J(["w-full font-medium border-gray-300 transition-all duration-200",Ne.value?"text-red-600 border-red-300 bg-red-50 hover:bg-red-100":"text-gray-700 hover:bg-gray-50"]),loading:I.value,onClick:k[6]||(k[6]=Ce(le=>ee(C.item),["stop"]))},{default:H(()=>[(c(),p("svg",{class:"w-4 h-4 mr-2",fill:Ne.value?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},k[17]||(k[17]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),8,Hl)),W(" "+S(Ne.value?"Remove from Watchlist":"Add to Watchlist"),1)]),_:1},8,["class","loading"])]))]),D(yt,{show:B.value,"onUpdate:show":k[7]||(k[7]=le=>B.value=le),title:"Sign in to add to watchlist",subtitle:"Create an account or sign in to save items to your watchlist and track your favorite auctions.",onSuccess:ue},null,8,["show"])])}}}),pr=Ae(Wl,[["__scopeId","data-v-9c64a7b0"]]),Kl={key:1},Yl={class:"text-center"},Ql={key:0,class:"mb-4"},Xl={class:"text-lg font-medium text-gray-900 mb-2"},Gl={class:"text-sm text-gray-600 mb-6"},Jl={class:"flex flex-col sm:flex-row gap-3 justify-center"},Zl=pe({__name:"AuthGuard",props:{title:{default:"Authentication Required"},message:{default:"Please sign in to continue with this action."},loginButtonText:{default:"Sign In"},registerButtonText:{default:"Create Account"},showRegisterButton:{type:Boolean,default:!0},showIcon:{type:Boolean,default:!0},fallbackStyle:{default:"card"},requireAuth:{type:Boolean,default:!0}},emits:["auth-required","auth-success"],setup(t,{expose:s,emit:r}){const o=t,a=r,n=ge(),u=P(!1),b=P("login"),w=q(()=>{if(!o.requireAuth)return!0;const T=window.user;if(T)return console.log("AuthGuard: User authenticated via server data",T),!0;const y=!!n.user,f=n.sessionAuth,_=!!n.token,R=n.isAuthenticated,M=y||f||R;return console.log("AuthGuard: authentication check",{serverUser:!!T,hasUser:y,hasSession:f,hasToken:_,storeAuth:R,finalResult:M}),M}),l=q(()=>n.isLoading),d=q(()=>{const T="auth-guard-fallback";switch(o.fallbackStyle){case"card":return`${T} bg-white rounded-lg border border-gray-200 p-8 shadow-sm`;case"inline":return`${T} py-6`;case"minimal":return`${T} py-4`;default:return`${T} bg-white rounded-lg border border-gray-200 p-8 shadow-sm`}}),I=(T="login")=>{b.value=T,u.value=!0,a("auth-required")},B=T=>{u.value=!1,a("auth-success",T)};return Te(async()=>{const T=window.user;T&&!n.user&&(console.log("AuthGuard: Syncing server user data with auth store"),n.setUser(T))}),s({showAuthModal:I,isAuthenticated:w}),(T,y)=>(c(),p("div",null,[w.value?tt(T.$slots,"default",{key:0},void 0,!0):(c(),p("div",Kl,[tt(T.$slots,"fallback",{},()=>[e("div",{class:J(d.value)},[e("div",Yl,[T.showIcon?(c(),p("div",Ql,[D(h(dt),{class:"mx-auto h-12 w-12 text-gray-400"})])):z("",!0),e("h3",Xl,S(T.title),1),e("p",Gl,S(T.message),1),e("div",Jl,[D(h(se),{variant:"primary",onClick:I,loading:l.value},{default:H(()=>[W(S(T.loginButtonText),1)]),_:1},8,["loading"]),T.showRegisterButton?(c(),ce(h(se),{key:0,variant:"outline",onClick:y[0]||(y[0]=f=>I("register")),loading:l.value},{default:H(()=>[W(S(T.registerButtonText),1)]),_:1},8,["loading"])):z("",!0)])])],2)],!0)])),D(yt,{show:u.value,"onUpdate:show":y[1]||(y[1]=f=>u.value=f),"start-with-register":b.value==="register",title:T.title,subtitle:T.message,onSuccess:B,onClose:y[2]||(y[2]=f=>u.value=!1)},null,8,["show","start-with-register","title","subtitle"])]))}}),is=Ae(Zl,[["__scopeId","data-v-7401f350"]]),ed={class:"auction-filters"},td={key:0,class:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg"},sd={class:"flex flex-wrap gap-2"},rd=["onClick"],od={class:"space-y-8"},ad={class:"filter-section"},nd={class:"space-y-3"},id=["value"],ld={class:"ml-3 text-sm text-gray-700 group-hover:text-gray-900 flex-1"},dd={key:0,class:"text-xs text-gray-500 font-medium"},ud={key:0,class:"filter-section"},cd={class:"space-y-3 max-h-48 overflow-y-auto"},md=["value"],fd={class:"ml-3 text-sm text-gray-700 group-hover:text-gray-900 flex-1"},pd={key:0,class:"text-xs text-gray-500 font-medium"},vd={class:"filter-section"},gd={class:"space-y-4"},hd={class:"grid grid-cols-2 gap-2"},yd=["onClick"],bd={class:"pt-2"},xd={class:"grid grid-cols-2 gap-3"},wd=pe({__name:"AuctionFilters",props:{auctionTypeOptions:{default:()=>[{label:"Daily Sale",value:"cash"},{label:"Online Auctions",value:"online"},{label:"Live Auctions",value:"live"}]},locationOptions:{default:()=>[{label:"Blantyre branch",value:"1"},{label:"Lilongwe branch",value:"2"}]},showLocationFilter:{type:Boolean,default:!0}},emits:["filter-change","apply-filters","reset-filters"],setup(t,{emit:s}){const r=t,o=s,a=P([]),n=P([]),u=P({min:"",max:""}),b=[{label:"Under 50,000",min:null,max:5e4},{label:"50,000 - 100,000",min:5e4,max:1e5},{label:"100,000 - 500,000",min:1e5,max:5e5},{label:"500,000 - 1,000,000",min:5e5,max:1e6},{label:"1,000,000 - 5,000,000",min:1e6,max:5e6},{label:"Over 5,000,000",min:5e6,max:null}],w=q(()=>a.value.length>0||n.value.length>0||u.value.min||u.value.max),l=q(()=>{const M=[];if(a.value.forEach(g=>{var x;const v=(x=r.auctionTypeOptions)==null?void 0:x.find(j=>j.value===g);v&&M.push({key:`type-${g}`,label:v.label,type:"auctionType",value:g})}),n.value.forEach(g=>{var x;const v=(x=r.locationOptions)==null?void 0:x.find(j=>j.value===g);v&&M.push({key:`location-${g}`,label:v.label,type:"location",value:g})}),u.value.min||u.value.max){const g=u.value.min||"0",v=u.value.max||"∞";M.push({key:"price-range",label:`${g} - ${v}`,type:"priceRange",value:"custom"})}return M}),d=q(()=>({auctionTypes:a.value,locations:n.value,priceMin:u.value.min?parseFloat(u.value.min):null,priceMax:u.value.max?parseFloat(u.value.max):null})),I=M=>{var g,v;u.value={min:((g=M.min)==null?void 0:g.toString())||"",max:((v=M.max)==null?void 0:v.toString())||""},f()},B=M=>{const g=u.value.min?parseFloat(u.value.min):null,v=u.value.max?parseFloat(u.value.max):null;return g===M.min&&v===M.max},T=()=>{f()},y=(M,g)=>{switch(M){case"auctionType":a.value=a.value.filter(v=>v!==g);break;case"location":n.value=n.value.filter(v=>v!==g);break;case"priceRange":u.value={min:"",max:""};break}f()},f=()=>{o("filter-change",d.value)},_=()=>{a.value=[],n.value=[],u.value={min:"",max:""},o("reset-filters")},R=()=>{_(),f()};return(M,g)=>(c(),p("div",ed,[w.value?(c(),p("div",td,[e("div",{class:"flex items-center justify-between mb-3"},[g[4]||(g[4]=e("h4",{class:"text-sm font-medium text-blue-900"},"Active Filters",-1)),e("button",{onClick:R,class:"text-xs text-blue-600 hover:text-blue-700 font-medium"}," Clear all ")]),e("div",sd,[(c(!0),p(me,null,fe(l.value,v=>(c(),p("span",{key:v.key,class:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white text-blue-800 border border-blue-200"},[W(S(v.label)+" ",1),e("button",{onClick:x=>y(v.type,v.value),class:"ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-100 transition-colors"},g[5]||(g[5]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,rd)]))),128))])])):z("",!0),e("div",od,[e("div",ad,[g[6]||(g[6]=e("h4",{class:"text-sm font-semibold text-gray-900 mb-4"},"Auction Type",-1)),e("div",nd,[(c(!0),p(me,null,fe(M.auctionTypeOptions,v=>(c(),p("label",{key:v.value,class:"flex items-center cursor-pointer group"},[he(e("input",{"onUpdate:modelValue":g[0]||(g[0]=x=>a.value=x),type:"checkbox",value:v.value,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors",onChange:f},null,40,id),[[Ye,a.value]]),e("span",ld,S(v.label),1),v.count!==void 0?(c(),p("span",dd,S(v.count),1)):z("",!0)]))),128))])]),M.showLocationFilter?(c(),p("div",ud,[g[7]||(g[7]=e("h4",{class:"text-sm font-semibold text-gray-900 mb-4"},"Location",-1)),e("div",cd,[(c(!0),p(me,null,fe(M.locationOptions,v=>(c(),p("label",{key:v.value,class:"flex items-center cursor-pointer group"},[he(e("input",{"onUpdate:modelValue":g[1]||(g[1]=x=>n.value=x),type:"checkbox",value:v.value,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors",onChange:f},null,40,md),[[Ye,n.value]]),e("span",fd,S(v.label),1),v.count!==void 0?(c(),p("span",pd,S(v.count),1)):z("",!0)]))),128))])])):z("",!0),e("div",vd,[g[10]||(g[10]=e("h4",{class:"text-sm font-semibold text-gray-900 mb-4"},"Price Range (MWK)",-1)),e("div",gd,[e("div",hd,[(c(),p(me,null,fe(b,v=>e("button",{key:v.label,onClick:x=>I(v),class:J(["px-3 py-2 text-xs border rounded-lg transition-all duration-200 font-medium text-center",B(v)?"bg-blue-600 border-blue-600 text-white":"bg-white border-gray-300 text-gray-700 hover:border-blue-300 hover:text-blue-600"])},S(v.label),11,yd)),64))]),e("div",bd,[e("div",xd,[e("div",null,[g[8]||(g[8]=e("label",{class:"block text-xs font-medium text-gray-600 mb-2"},"Min Price",-1)),he(e("input",{"onUpdate:modelValue":g[2]||(g[2]=v=>u.value.min=v),type:"number",min:"0",step:"1",placeholder:"0",class:"block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onChange:T},null,544),[[Pe,u.value.min]])]),e("div",null,[g[9]||(g[9]=e("label",{class:"block text-xs font-medium text-gray-600 mb-2"},"Max Price",-1)),he(e("input",{"onUpdate:modelValue":g[3]||(g[3]=v=>u.value.max=v),type:"number",min:"0",step:"1",placeholder:"Any",class:"block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onChange:T},null,544),[[Pe,u.value.max]])])])])])])])]))}}),_d=Ae(wd,[["__scopeId","data-v-76900880"]]),kd={class:"min-h-screen bg-gray-50"},Cd={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},$d=pe({__name:"AppLayout",setup(t){return(s,r)=>(c(),p("div",kd,[r[0]||(r[0]=ze('<nav class="bg-white shadow"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-16"><div class="flex"><div class="flex-shrink-0 flex items-center"><h1 class="text-xl font-bold text-gray-900"> Vertigo AMS </h1></div><div class="hidden sm:ml-6 sm:flex sm:space-x-8"><a href="#" class="border-primary-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"> Dashboard </a><a href="#" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"> Auctions </a><a href="#" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"> Items </a></div></div><div class="hidden sm:ml-6 sm:flex sm:items-center"><div class="ml-3 relative"><button type="button" class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" id="user-menu-button" aria-expanded="false" aria-haspopup="true"><span class="sr-only">Open user menu</span><div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center"><span class="text-sm font-medium text-gray-700">U</span></div></button></div></div></div></div></nav>',1)),e("main",null,[e("div",Cd,[tt(s.$slots,"default")])])]))}}),Sd={class:"relative"},Md={key:0,class:"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium"},Ad={class:"flex items-center justify-between p-4 border-b border-gray-200"},jd={class:"text-lg font-semibold text-gray-900"},Td={class:"flex-1 overflow-y-auto p-4"},Id={key:0,class:"text-center py-8"},Bd={key:1,class:"space-y-4"},Ed={class:"flex-shrink-0"},Nd=["src","alt"],Dd={class:"flex-1 min-w-0"},Pd={class:"text-sm font-medium text-gray-900 truncate"},Rd={class:"text-sm text-gray-500"},Ld={class:"mt-1"},zd={class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},Od={class:"flex-shrink-0"},Fd=["onClick","disabled"],Vd={key:0,class:"border-t border-gray-200 p-4 space-y-4"},qd={class:"flex justify-between items-center"},Ud={class:"text-lg font-semibold text-gray-900"},Hd={class:"space-y-2"},Wd={key:1,class:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"},vr=pe({__name:"CartDrawer",setup(t){const s=Le(),r=P(!1),o=()=>{r.value=!r.value},a=()=>{r.value=!1},n=async l=>{await s.removeFromCart(l)&&console.log(`${l.name} removed from cart`)},u=()=>{s.clearCart(),a()},b=()=>{a(),window.location.href="/checkout"},w=l=>{switch(l){case"cash":return"Daily Sale";case"online":return"Online Auction";case"live":return"Live Auction";default:return l||"Unknown"}};return(l,d)=>(c(),p("div",Sd,[e("button",{onClick:o,class:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md transition-colors duration-200"},[d[1]||(d[1]=e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),h(s).cartCount>0?(c(),p("span",Md,S(h(s).cartCount>9?"9+":h(s).cartCount),1)):z("",!0)]),r.value?(c(),p("div",{key:0,class:"fixed inset-0 z-50 overflow-hidden",onClick:a},[d[9]||(d[9]=e("div",{class:"absolute inset-0 bg-black bg-opacity-50 transition-opacity"},null,-1)),e("div",{class:"absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl transform transition-transform duration-300 ease-in-out",onClick:d[0]||(d[0]=Ce(()=>{},["stop"]))},[e("div",Ad,[e("h2",jd," Shopping Cart ("+S(h(s).cartCount)+") ",1),e("button",{onClick:a,class:"p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md"},d[2]||(d[2]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Td,[h(s).isEmpty?(c(),p("div",Id,d[3]||(d[3]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Your cart is empty",-1),e("p",{class:"text-xs text-gray-400"},"Add some items to get started",-1)]))):(c(),p("div",Bd,[(c(!0),p(me,null,fe(h(s).items,I=>{var B;return c(),p("div",{key:I.id,class:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"},[e("div",Ed,[e("img",{src:I.image||"/images/placeholder.jpg",alt:I.name,class:"w-16 h-16 object-cover rounded-md"},null,8,Nd)]),e("div",Dd,[e("h3",Pd,S(I.name),1),e("p",Rd,S(h(s).formatCurrency(I.target_amount||0)),1),e("div",Ld,[e("span",zd,S(w((B=I.auction_type)==null?void 0:B.type)),1)])]),e("div",Od,[e("button",{onClick:T=>n(I),disabled:h(s).loading,class:"p-1 text-red-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 rounded-md disabled:opacity-50"},d[4]||(d[4]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Fd)])])}),128))]))]),h(s).isEmpty?z("",!0):(c(),p("div",Vd,[e("div",qd,[d[5]||(d[5]=e("span",{class:"text-base font-medium text-gray-900"},"Total:",-1)),e("span",Ud,S(h(s).formatCurrency(h(s).cartTotal)),1)]),e("div",Hd,[D(h(se),{onClick:b,class:"w-full bg-blue-600 hover:bg-blue-700 text-white",disabled:h(s).loading},{default:H(()=>d[6]||(d[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})],-1),W(" Proceed to Checkout ")])),_:1,__:[6]},8,["disabled"]),D(h(se),{onClick:u,variant:"outline",class:"w-full border-gray-300 text-gray-700 hover:bg-gray-50",disabled:h(s).loading},{default:H(()=>d[7]||(d[7]=[W(" Clear Cart ")])),_:1,__:[7]},8,["disabled"])])])),h(s).loading?(c(),p("div",Wd,d[8]||(d[8]=[e("div",{class:"text-center"},[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e("p",{class:"mt-2 text-sm text-gray-500"},"Updating cart...")],-1)]))):z("",!0)])])):z("",!0)]))}}),Kd={class:"relative"},Yd=pe({__name:"CartIcon",props:{count:{default:0},buttonClass:{default:""},badgeClass:{default:""}},emits:["click"],setup(t){return(s,r)=>(c(),p("div",Kd,[e("button",{onClick:r[0]||(r[0]=o=>s.$emit("click")),class:J(["relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md transition-colors duration-200",s.buttonClass])},[r[1]||(r[1]=e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),s.count>0?(c(),p("span",{key:0,class:J(["absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium",s.badgeClass])},S(s.count>99?"99+":s.count),3)):z("",!0)],2)]))}}),Qd={key:0,class:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"},Xd=pe({__name:"CartNavIcon",setup(t){const s=Le(),r=q(()=>{const o=window.location.pathname;return o==="/cart"||o.startsWith("/spa")||o.startsWith("/home-vue")});return(o,a)=>(c(),ce(ot(r.value?"router-link":"a"),{to:r.value?"/cart":void 0,href:r.value?void 0:"/cart",class:"relative p-2 text-gray-600 hover:text-primary-600 rounded-lg hover:bg-white/50 transition-all duration-200 inline-block"},{default:H(()=>[a[0]||(a[0]=e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),h(s).cartCount>0?(c(),p("span",Qd,S(h(s).cartCount>9?"9+":h(s).cartCount),1)):z("",!0)]),_:1,__:[0]},8,["to","href"]))}}),Gd={class:"bg-white border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200"},Jd={class:"p-2"},Zd={class:"flex items-start space-x-4"},eu={class:"flex-shrink-0"},tu={class:"relative"},su=["src","alt"],ru={class:"absolute -top-1 -right-1"},ou={class:"flex-1 min-w-0"},au={class:"flex items-start justify-between"},nu={class:"flex-1 pr-4"},iu={class:"text-lg font-semibold text-gray-900 mb-2 leading-tight hover:text-blue-600 transition-colors cursor-pointer"},lu={class:"flex items-center flex-wrap gap-2 mb-2"},du={class:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded"},uu={key:0,class:"text-sm text-gray-600 line-clamp-1 mb-2"},cu={class:"flex items-center space-x-3"},mu={class:"text-lg font-bold text-gray-900"},fu={key:0,class:"text-sm text-gray-500"},pu={class:"flex items-center space-x-1"},vu={class:"mb-2 pt-3 border-t border-gray-100"},gu={class:"flex items-center justify-between px-3"},hu={class:"flex items-center space-x-3"},yu={class:"flex items-center border border-gray-300 rounded-md bg-white"},bu=["disabled"],xu={class:"text-right"},wu={class:"text-lg font-bold text-gray-900"},_u=pe({__name:"CartItemRow",props:{item:{},loading:{type:Boolean,default:!1}},emits:["update-quantity","remove"],setup(t,{emit:s}){const r=t,o=s,a=P(r.item.quantity||1),n=q(()=>{const y=r.item.target_amount||0,f=a.value||1;return y*f});Me(()=>r.item.quantity,y=>{y!==void 0&&y!==a.value&&(a.value=y)});const u=y=>!y||y<=0?"MK 0":`MK ${y.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,b=y=>{switch(y){case"cash":return"success";case"online":return"primary";case"live":return"danger";default:return"secondary"}},w=y=>{switch(y){case"cash":return"Cash Auction";case"online":return"Online Auction";case"live":return"Live Auction";default:return"Unknown"}},l=y=>{switch(y){case"cash":return"bg-green-500";case"online":return"bg-blue-500";case"live":return"bg-red-500";default:return"bg-gray-500"}},d=()=>{a.value<99&&(a.value++,B())},I=()=>{a.value>1&&(a.value--,B())},B=()=>{a.value<1?a.value=1:a.value>99&&(a.value=99),a.value!==r.item.quantity&&o("update-quantity",r.item,a.value)},T=y=>{const f=y.target;f.src="/img/product.jpeg"};return(y,f)=>{var _,R;return c(),p("div",Gd,[e("div",Jd,[e("div",Zd,[e("div",eu,[e("div",tu,[e("img",{src:y.item.image||y.item.cropped||"/img/product.jpeg",alt:y.item.name,class:"w-16 h-16 object-cover rounded-lg border border-gray-200",onError:T},null,40,su),e("div",ru,[e("div",{class:J([l((_=y.item.auction_type)==null?void 0:_.type),"w-4 h-4 rounded-full border-2 border-white shadow-sm"])},null,2)])])]),e("div",ou,[e("div",au,[e("div",nu,[e("h4",iu,S(y.item.name),1),e("div",lu,[D(h(Qe),{variant:b((R=y.item.auction_type)==null?void 0:R.type),size:"sm",class:"font-medium px-2 py-1 text-xs"},{default:H(()=>{var M;return[W(S(w((M=y.item.auction_type)==null?void 0:M.type)),1)]}),_:1},8,["variant"]),e("span",du," ID: "+S(y.item.id),1)]),y.item.description?(c(),p("p",uu,S(y.item.description),1)):z("",!0),e("div",cu,[e("div",mu,S(u(y.item.target_amount)),1),(y.item.quantity||1)>1?(c(),p("div",fu," × "+S(y.item.quantity||1),1)):z("",!0)])]),e("div",pu,[D(h(se),{variant:"ghost",size:"sm",onClick:f[0]||(f[0]=M=>y.$emit("remove",y.item)),disabled:y.loading,class:"text-gray-400 hover:text-red-600 hover:bg-red-50 p-2 rounded-lg transition-all duration-200",title:"Remove item from cart"},{default:H(()=>f[2]||(f[2]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),_:1,__:[2]},8,["disabled"])])])])])]),e("div",vu,[e("div",gu,[e("div",hu,[f[5]||(f[5]=e("span",{class:"text-sm text-gray-600 font-medium"},"Qty:",-1)),e("div",yu,[D(h(se),{variant:"ghost",size:"xs",onClick:I,disabled:y.loading||(y.item.quantity||1)<=1,class:"px-2 py-1 text-gray-500 hover:text-gray-700 hover:bg-gray-50"},{default:H(()=>f[3]||(f[3]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 12H4"})],-1)])),_:1,__:[3]},8,["disabled"]),he(e("input",{"onUpdate:modelValue":f[1]||(f[1]=M=>a.value=M),type:"number",min:"1",max:"99",class:"w-12 px-2 py-1 text-center text-sm font-medium border-0 focus:ring-0 focus:outline-none",onBlur:B,onKeyup:_r(B,["enter"]),disabled:y.loading},null,40,bu),[[Pe,a.value,void 0,{number:!0}]]),D(h(se),{variant:"ghost",size:"xs",onClick:d,disabled:y.loading||(y.item.quantity||1)>=99,class:"px-2 py-1 text-gray-500 hover:text-gray-700 hover:bg-gray-50"},{default:H(()=>f[4]||(f[4]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)])),_:1,__:[4]},8,["disabled"])])]),e("div",xu,[f[6]||(f[6]=e("div",{class:"text-xs text-gray-500 mb-1"},"Subtotal",-1)),e("div",wu,S(u(n.value)),1)])])])])}}});const lt=Ae(_u,[["__scopeId","data-v-c5ab099c"]]),gr=at("items",()=>{const t=P(null),s=P([]),r=P([]),o=P(!1),a=P(null),n=q(()=>{var A;return((A=t.value)==null?void 0:A.total)||0}),u=q(()=>{var A;return((A=t.value)==null?void 0:A.current_page)||1}),b=q(()=>{var A;return((A=t.value)==null?void 0:A.last_page)||1}),w=q(()=>u.value<b.value),l=async(A={})=>{o.value=!0,a.value=null;try{const O=new URLSearchParams;A.search&&O.append("search",A.search),A.type&&O.append("type",A.type),A.branch_id&&O.append("branch_id",A.branch_id),A.auction_type_id&&O.append("auction_type_id",A.auction_type_id),A.per_page&&O.append("per_page",A.per_page.toString()),A.sort_by&&O.append("sort_by",A.sort_by),A.statuses&&A.statuses.length>0&&A.statuses.forEach(oe=>O.append("statuses[]",oe)),A.auctionTypes&&A.auctionTypes.length>0&&A.auctionTypes.forEach(oe=>O.append("auction_types[]",oe)),A.locations&&A.locations.length>0&&A.locations.forEach(oe=>O.append("locations[]",oe)),A.conditions&&A.conditions.length>0&&A.conditions.forEach(oe=>O.append("conditions[]",oe)),A.priceMin!==void 0&&A.priceMin!==null&&O.append("price_min",A.priceMin.toString()),A.priceMax!==void 0&&A.priceMax!==null&&O.append("price_max",A.priceMax.toString()),A.dateRange&&O.append("date_range",A.dateRange),A.customDateRange&&(A.customDateRange.from&&O.append("date_from",A.customDateRange.from),A.customDateRange.to&&O.append("date_to",A.customDateRange.to)),A.hasImages&&O.append("has_images","1"),A.hasReserve&&O.append("has_reserve","1"),A.featuredOnly&&O.append("featured_only","1"),A.acceptsPayPal&&O.append("accepts_paypal","1"),A.freeShipping&&O.append("free_shipping","1");const Z=await ke.get(`/ajax-items?${O.toString()}`);t.value=Z.data}catch(O){a.value="Failed to fetch auction items",console.error("Error fetching items:",O)}finally{o.value=!1}};return{items:t,branches:s,auctionTypes:r,loading:o,error:a,totalItems:n,currentPage:u,lastPage:b,hasMorePages:w,fetchItems:l,fetchBranches:async()=>{try{const A=await ke.get("/ajax-branches");s.value=A.data}catch(A){console.error("Error fetching branches:",A),s.value=[]}},fetchAuctionTypes:async()=>{try{const A=await ke.get("/ajax-auction-types");r.value=A.data}catch(A){console.error("Error fetching auction types:",A),r.value=[]}},getItemById:async A=>{try{return(await ke.get(`/ajax-item/${A}`)).data}catch(O){return console.error("Error fetching item:",O),null}},placeBid:async(A,O)=>{try{const Z=await ke.post(`/place-a-bid/${A}`,{bid_amount:O});return await l(),Z.data}catch(Z){throw console.error("Error placing bid:",Z),Z}},addToCart:async A=>{try{return(await ke.get(`/add-to-cart/${A}`)).data}catch(O){throw console.error("Error adding to cart:",O),O}},removeFromCart:async A=>{try{return(await ke.get(`/remove-from-cart/${A}`)).data}catch(O){throw console.error("Error removing from cart:",O),O}},initializeBranches:A=>{s.value=A},formatCurrency:A=>!A||A<=0?"MK 0":`MK ${A.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,getItemTypeLabel:A=>{switch(A){case"cash":return"Daily Sale";case"online":return"Online Auction";case"live":return"Live Auction";default:return A}},getItemActionLabel:A=>{switch(A){case"cash":return"VIEW DETAILS";case"online":case"live":return"PLACE A BID";default:return"VIEW DETAILS"}},getPriceLabel:A=>{switch(A){case"cash":return"Price:";case"online":case"live":return"Current Bid:";default:return"Price:"}},getItemPrice:A=>{var O;switch((O=A.auction_type)==null?void 0:O.type){case"cash":return A.target_amount||0;case"online":case"live":return A.bid_amount||0;default:return A.target_amount||0}},reset:()=>{t.value=null,s.value=[],r.value=[],o.value=!1,a.value=null}}}),hr=at("adverts",()=>{const t=P([]),s=P(!1),r=P(null);return{adverts:t,loading:s,error:r,fetchAdverts:async()=>{s.value=!0,r.value=null;try{const u=await ke.get("/api/adverts");t.value=u.data}catch(u){r.value="Failed to fetch advertisements",console.error("Error fetching adverts:",u),t.value=[]}finally{s.value=!1}},initializeAdverts:u=>{t.value=u},reset:()=>{t.value=[],s.value=!1,r.value=null}}}),ku={class:"homepage"},Cu={key:0,class:"hero-section relative overflow-hidden"},$u={class:"py-16 lg:py-12"},Su={class:"text-center max-w-5xl mx-auto"},Mu={class:"flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-4 mb-10"},Au={class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",style:{color:"#0068ff"}},ju={class:"absolute bottom-0 left-0 w-full overflow-hidden leading-none"},Tu={class:"relative block w-full h-16 md:h-20 lg:h-24 text-white",fill:"currentColor",viewBox:"0 0 1200 120",preserveAspectRatio:"none",style:{transform:"rotate(180deg)"}},Iu={class:"category-navigation bg-white border-b relative z-10"},Bu={class:"py-6"},Eu={class:"flex justify-center"},Nu={class:"relative w-full max-w-lg"},Du={id:"auctions",class:"auctions-section py-8 bg-white"},Pu={class:"flex flex-col xl:flex-row gap-6"},Ru={class:"w-full xl:w-72 flex-shrink-0"},Lu={class:"sticky top-6"},zu={class:"bg-white border border-gray-200 rounded-xl overflow-hidden"},Ou={class:"px-6 py-4 border-b border-gray-100 bg-gray-50"},Fu={class:"flex items-center justify-between"},Vu={class:"p-6"},qu={class:"flex-1 min-w-0"},Uu={class:"mb-6"},Hu={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"},Wu={class:"flex items-center space-x-4"},Ku={class:"text-2xl font-bold text-gray-900"},Yu={key:0,class:"text-sm text-gray-600 bg-gray-100 px-3 py-1.5 rounded-lg font-medium"},Qu={class:"flex items-center space-x-3"},Xu={key:0,class:"flex justify-center py-16"},Gu={class:"text-center"},Ju={key:1,class:"space-y-8"},Zu={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},ec={key:0,class:"flex justify-center pt-8"},tc={key:0,class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},sc={key:2,class:"text-center py-16"},rc={class:"max-w-md mx-auto"},oc={class:"space-y-3"},ac={class:"stats-section bg-gray-50 py-16"},nc={key:1,class:"fixed bottom-6 right-6 z-50"},ic={class:"absolute -top-2 -right-2 h-6 w-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium"},lc=pe({__name:"Homepage",setup(t){const s=gr(),r=hr(),o=Le(),a=Be(),n=qe(),u=Ns(),b=P(!0),w=P(!1),l=P(""),d=P("cash"),I=P(1),B=P(""),T=P(""),y=P(10),f=P("newest"),_=P({}),R=q(()=>s.items),M=q(()=>s.branches),g=q(()=>s.auctionTypes),v=q(()=>r.adverts),x=q(()=>{if(!v.value)return[];const m=new Date;return v.value.filter(i=>i.date_to?new Date(i.date_to)>m:!0)}),j=q(()=>R.value?R.value.current_page>=R.value.last_page:!0),A=q(()=>{var m;return[{key:"all",label:"All Items",badge:((m=R.value)==null?void 0:m.total)||0},{key:"cash",label:"Daily Sale",badge:void 0},{key:"online",label:"Online Auctions",badge:void 0},{key:"live",label:"Live Auctions",badge:void 0}]}),O=q(()=>g.value.map(m=>({value:m.type,label:m.name}))),Z=q(()=>M.value&&M.value.length>0?M.value.map(m=>({value:m.id.toString(),label:m.name})):[{label:"Blantyre branch",value:"1"},{label:"Lilongwe branch",value:"2"}]),oe=q(()=>l.value||d.value!=="cash"||Object.keys(_.value).length>0),de=async()=>{b.value=!0;try{const m={search:l.value,type:d.value,branch_id:B.value,auction_type_id:T.value,per_page:y.value,sort_by:f.value,..._.value};await s.fetchItems(m)}finally{b.value=!1}},te=async()=>{w.value=!0;try{y.value+=10,await de()}finally{w.value=!1}},V=(m,i)=>{I.value=m;const K=i.key;K==="all"?d.value="":d.value=K,T.value="",de()},Y=()=>{l.value="",de()},ne=uo(()=>{de()},300),L=()=>{var m;(m=document.getElementById("auctions"))==null||m.scrollIntoView({behavior:"smooth"})},$=()=>{n.push("/about")},F=m=>{n.push(`/item/${m.id}`)},U=m=>{n.push(`/item/${m.id}`)},Q=m=>{n.push(`/item/${m.id}`)},ee=async m=>{var K;console.log("Add to cart:",m.id),await o.addToCart(m)?(a.success(`${m.name} added to cart`),o.trackCartEvent("item_added",{item_id:m.id,item_name:m.name,item_price:m.target_amount,auction_type:(K=m.auction_type)==null?void 0:K.type,source_page:"homepage"})):a.error(o.error||"Failed to add item to cart")},ue=async m=>{var K;console.log("Remove from cart:",m.id),await o.removeFromCart(m)?(a.success(`${m.name} removed from cart`),o.trackCartEvent("item_removed",{item_id:m.id,item_name:m.name,item_price:m.target_amount,auction_type:(K=m.auction_type)==null?void 0:K.type,source_page:"homepage"})):a.error(o.error||"Failed to remove item from cart")},xe=async m=>{console.log("Checkout item:",m.id),await o.addToCart(m,!0)||a.error("Failed to proceed to checkout")},Re=m=>{console.log("Watch item:",m.id)},Ne=m=>{_.value=m,de()},je=()=>{_.value={},l.value="",d.value="cash",I.value=1,de()},Oe=()=>{de()},C=()=>({"":"All Items",cash:"Daily Sale Items",online:"Online Auction Items",live:"Live Auction Items"})[d.value]||"Auction Items",k=()=>{var N,G,ie,le;const m=new URLSearchParams;if(l.value&&m.set("search",l.value),d.value&&m.set("type",d.value),f.value&&f.value!=="newest"&&m.set("sort",f.value),_.value){const re=_.value;(N=re.statuses)!=null&&N.length&&m.set("statuses",re.statuses.join(",")),(G=re.auctionTypes)!=null&&G.length&&m.set("auction_types",re.auctionTypes.join(",")),(ie=re.locations)!=null&&ie.length&&m.set("locations",re.locations.join(",")),(le=re.conditions)!=null&&le.length&&m.set("conditions",re.conditions.join(",")),re.priceMin&&m.set("price_min",re.priceMin.toString()),re.priceMax&&m.set("price_max",re.priceMax.toString()),re.hasImages&&m.set("has_images","1"),re.hasReserve&&m.set("has_reserve","1"),re.featuredOnly&&m.set("featured","1")}const i=m.toString(),K=i?`${window.location.pathname}?${i}`:window.location.pathname;window.history.replaceState({},"",K)},X=()=>{var K,N,G,ie;const m=new URLSearchParams(window.location.search);if(m.get("search")&&(l.value=m.get("search")||""),m.get("type")){const le=m.get("type")||"";d.value=le;const re=A.value.findIndex(we=>we.key===(le||"all"));re!==-1&&(I.value=re)}m.get("sort")&&(f.value=m.get("sort")||"newest");const i={};m.get("statuses")&&(i.statuses=((K=m.get("statuses"))==null?void 0:K.split(","))||[]),m.get("auction_types")&&(i.auctionTypes=((N=m.get("auction_types"))==null?void 0:N.split(","))||[]),m.get("locations")&&(i.locations=((G=m.get("locations"))==null?void 0:G.split(","))||[]),m.get("conditions")&&(i.conditions=((ie=m.get("conditions"))==null?void 0:ie.split(","))||[]),m.get("price_min")&&(i.priceMin=parseFloat(m.get("price_min")||"0")),m.get("price_max")&&(i.priceMax=parseFloat(m.get("price_max")||"0")),m.get("has_images")&&(i.hasImages=!0),m.get("has_reserve")&&(i.hasReserve=!0),m.get("featured")&&(i.featuredOnly=!0),Object.keys(i).length>0&&(_.value=i)};return Me([l,d,f,_],()=>{k()},{deep:!0}),Te(async()=>{if(X(),o.isEmpty){const m=window.cartItems||[];m.length>0?o.initializeCart(m):await o.fetchCart()}o.setLastShoppingPage(window.location.href),await Promise.all([s.fetchBranches(),s.fetchAuctionTypes(),r.fetchAdverts(),de()])}),Me(()=>u.path,m=>{(m==="/"||m==="/homepage")&&o.setLastShoppingPage(window.location.href)},{immediate:!1}),(m,i)=>{const K=Zt("router-link");return c(),p("div",ku,[D(h(Os),{adverts:x.value},null,8,["adverts"]),!x.value||x.value.length===0?(c(),p("section",Cu,[i[11]||(i[11]=ze('<div class="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-900" data-v-87e05839></div><div class="absolute inset-0 bg-black bg-opacity-20" data-v-87e05839></div><div class="absolute top-0 left-0 w-full h-full" data-v-87e05839><div class="absolute top-20 left-10 w-72 h-72 bg-white bg-opacity-10 rounded-full blur-3xl" data-v-87e05839></div><div class="absolute bottom-20 right-10 w-96 h-96 bg-blue-300 bg-opacity-20 rounded-full blur-3xl" data-v-87e05839></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-radial from-white/5 to-transparent rounded-full" data-v-87e05839></div></div>',3)),D(h($e),{class:"relative z-10"},{default:H(()=>[e("div",$u,[e("div",Su,[i[7]||(i[7]=e("div",{class:"inline-flex items-center px-4 py-2 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-white text-sm font-medium mb-6 border border-white border-opacity-30"},[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" Trusted by thousands of auctioneers in Malawi ")],-1)),i[8]||(i[8]=e("h1",{class:"text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight"},[W(" Welcome to "),e("span",{class:"block bg-gradient-to-r from-blue-200 to-white bg-clip-text text-transparent"}," Trust Auctioneers ")],-1)),i[9]||(i[9]=e("p",{class:"text-xl lg:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed"}," Experience the future of auction management with our cutting-edge platform. Discover exceptional items, place confident bids, and join a community of passionate collectors. ",-1)),e("div",Mu,[D(h(se),{variant:"primary",size:"md",onClick:L,class:"bg-white text-blue-600 hover:bg-blue-50 hover:text-blue-700 px-6 py-3 text-base font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border-0",style:{color:"#0068ff !important"}},{default:H(()=>[(c(),p("svg",Au,i[4]||(i[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"},null,-1)]))),i[5]||(i[5]=W(" Explore Live Auctions "))]),_:1,__:[5]}),e("button",{onClick:$,class:"border-2 border-white text-white hover:bg-white hover:text-blue-600 px-6 py-3 text-base font-semibold rounded-lg backdrop-blur-sm transition-all duration-300 inline-flex items-center"},i[6]||(i[6]=[e("svg",{class:"w-4 h-4 mr-2 text-white hover:text-blue-600 transition-colors duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),W(" Learn More ")]))])])])]),_:1}),e("div",ju,[(c(),p("svg",Tu,i[10]||(i[10]=[e("path",{d:"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z",opacity:".25"},null,-1),e("path",{d:"M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z",opacity:".5"},null,-1),e("path",{d:"M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"},null,-1)])))])])):z("",!0),e("section",Iu,[D(h($e),null,{default:H(()=>[e("div",Bu,[D(h(qa),{modelValue:I.value,"onUpdate:modelValue":i[0]||(i[0]=N=>I.value=N),tabs:A.value,variant:"underline",size:"lg",centered:"",onTabChange:V,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",Eu,[e("div",Nu,[i[13]||(i[13]=e("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[e("svg",{class:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1)),he(e("input",{type:"text","onUpdate:modelValue":i[1]||(i[1]=N=>l.value=N),placeholder:"Search auctions...",class:"pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full",onInput:i[2]||(i[2]=(...N)=>h(ne)&&h(ne)(...N))},null,544),[[Pe,l.value]]),l.value?(c(),p("button",{key:0,onClick:Y,class:"absolute inset-y-0 right-0 pr-3 flex items-center"},i[12]||(i[12]=[e("svg",{class:"h-4 w-4 text-gray-400 hover:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):z("",!0)])])])]),_:1})]),e("section",Du,[D(h($e),null,{default:H(()=>{var N;return[e("div",Pu,[e("aside",Ru,[e("div",Lu,[e("div",zu,[e("div",Ou,[e("div",Fu,[i[14]||(i[14]=e("h3",{class:"text-base font-semibold text-gray-900"},"Filters",-1)),oe.value?(c(),p("button",{key:0,onClick:je,class:"text-xs text-blue-600 hover:text-blue-700 font-medium"}," Clear all ")):z("",!0)])]),e("div",Vu,[D(h(_d),{"auction-type-options":O.value,"location-options":Z.value,"show-location-filter":!0,onFilterChange:Ne,onResetFilters:je},null,8,["auction-type-options","location-options"])])])])]),e("main",qu,[e("div",Uu,[e("div",Hu,[e("div",Wu,[e("h1",Ku,S(C()),1),(N=R.value)!=null&&N.total?(c(),p("span",Yu,S(R.value.total.toLocaleString())+" "+S(R.value.total===1?"item":"items"),1)):z("",!0)]),e("div",Qu,[i[16]||(i[16]=e("span",{class:"text-sm text-gray-600 font-medium"},"Sort by",-1)),he(e("select",{"onUpdate:modelValue":i[3]||(i[3]=G=>f.value=G),onChange:Oe,class:"text-sm border border-gray-300 rounded-lg px-4 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-medium"},i[15]||(i[15]=[e("option",{value:"newest"},"Newest First",-1),e("option",{value:"oldest"},"Oldest First",-1),e("option",{value:"price_low"},"Price: Low to High",-1),e("option",{value:"price_high"},"Price: High to Low",-1),e("option",{value:"ending_soon"},"Ending Soon",-1)]),544),[[Bs,f.value]])])])]),b.value?(c(),p("div",Xu,[e("div",Gu,[D(h(Ue),{size:"lg"}),i[17]||(i[17]=e("p",{class:"mt-4 text-sm text-gray-500"},"Loading auction items...",-1))])])):R.value&&R.value.data&&R.value.data.length>0?(c(),p("div",Ju,[e("div",Zu,[(c(!0),p(me,null,fe(R.value.data,G=>(c(),ce(h(pr),{key:G.id,item:G,onViewDetails:F,onPlaceBid:U,onPreview:Q,onAddToCart:ee,onRemoveFromCart:ue,onCheckout:xe,onWatchItem:Re},null,8,["item"]))),128))]),j.value?z("",!0):(c(),p("div",ec,[D(h(se),{variant:"outline",size:"lg",onClick:te,loading:w.value,class:"px-8 py-3 bg-white border-2 border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-all duration-200 rounded-xl font-medium"},{default:H(()=>[w.value?z("",!0):(c(),p("svg",tc,i[18]||(i[18]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]))),W(" "+S(w.value?"Loading...":"Load More Items"),1)]),_:1},8,["loading"])]))])):(c(),p("div",sc,[e("div",rc,[i[21]||(i[21]=e("div",{class:"w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1)),i[22]||(i[22]=e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"No auction items found",-1)),i[23]||(i[23]=e("p",{class:"text-gray-600 mb-6"},"We couldn't find any items matching your criteria. Try adjusting your filters or search terms.",-1)),e("div",oc,[D(h(se),{variant:"primary",onClick:je,class:"px-6 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium"},{default:H(()=>i[19]||(i[19]=[W(" Clear All Filters ")])),_:1,__:[19]}),i[20]||(i[20]=e("p",{class:"text-sm text-gray-500"},"or check back later for new auctions",-1))])])]))])])]}),_:1})]),e("section",ac,[D(h($e),null,{default:H(()=>i[24]||(i[24]=[e("div",{class:"text-center mb-12"},[e("h2",{class:"text-3xl font-bold text-gray-900 mb-4"},"Why Choose Vertigo AMS?"),e("p",{class:"text-lg text-gray-600 max-w-2xl mx-auto"}," Join thousands of satisfied customers who trust our platform for their auction needs ")],-1),e("div",{class:"grid grid-cols-1 md:grid-cols-3 gap-8"},[e("div",{class:"text-center"},[e("div",{class:"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])]),e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"Secure Bidding"),e("p",{class:"text-gray-600"},"Safe and secure bidding process with verified payments")]),e("div",{class:"text-center"},[e("div",{class:"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])]),e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"Real-time Updates"),e("p",{class:"text-gray-600"},"Live auction updates and instant bid notifications")]),e("div",{class:"text-center"},[e("div",{class:"bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"Trusted Platform"),e("p",{class:"text-gray-600"},"Established reputation with thousands of successful auctions")])],-1)])),_:1,__:[24]})]),D(h(vr)),D(h(Ds)),h(o).cartCount>0?(c(),p("div",nc,[D(K,{to:"/cart",class:"flex items-center justify-center w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"},{default:H(()=>[i[25]||(i[25]=e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),e("span",ic,S(h(o).cartCount>9?"9+":h(o).cartCount),1)]),_:1,__:[25]})])):z("",!0)])}}});const Pt=Ae(lc,[["__scopeId","data-v-87e05839"]]),dc={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},uc={class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},cc={class:"py-6"},mc={class:"flex items-center justify-between"},fc={class:"flex items-center space-x-4"},pc={class:"text-2xl font-bold text-gray-100 tracking-tight"},vc={key:0,class:"flex items-center space-x-3"},gc={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},hc={key:0,class:"flex justify-center py-20"},yc={class:"text-center"},bc={key:1,class:"text-center py-20"},xc={class:"max-w-4xl mx-auto"},wc={class:"bg-white rounded-3xl shadow-xl p-12 mb-8"},_c={class:"mb-10"},kc={key:2,class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Cc={class:"lg:col-span-2 space-y-4"},$c={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},Sc={class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},Mc={class:"flex items-center justify-between"},Ac={class:"flex items-center space-x-3"},jc={class:"flex items-center space-x-3"},Tc={class:"text-xs text-gray-600"},Ic={class:"flex items-center space-x-3"},Bc={class:"text-right"},Ec={class:"text-lg font-bold text-gray-900"},Nc={class:"flex items-center space-x-1"},Dc={class:"p-6"},Pc={class:"space-y-3"},Rc={key:0,class:"space-y-6"},Lc={key:0,class:"bg-white rounded-lg shadow-sm border border-gray-200"},zc={class:"px-6 py-3 border-b border-gray-200 bg-green-50"},Oc={class:"flex items-center justify-between"},Fc={class:"flex items-center space-x-2"},Vc={class:"text-xs text-green-700 bg-green-100 px-2 py-1 rounded-full font-medium"},qc={class:"text-sm font-medium text-green-700"},Uc={class:"p-4 space-y-3"},Hc={key:1,class:"bg-white rounded-lg shadow-sm border border-gray-200"},Wc={class:"px-6 py-3 border-b border-gray-200 bg-blue-50"},Kc={class:"flex items-center justify-between"},Yc={class:"flex items-center space-x-2"},Qc={class:"text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded-full font-medium"},Xc={class:"text-sm font-medium text-blue-700"},Gc={class:"p-4 space-y-3"},Jc={key:2,class:"bg-white rounded-lg shadow-sm border border-gray-200"},Zc={class:"px-6 py-3 border-b border-gray-200 bg-red-50"},e0={class:"flex items-center justify-between"},t0={class:"flex items-center space-x-2"},s0={class:"text-xs text-red-700 bg-red-100 px-2 py-1 rounded-full font-medium"},r0={class:"text-sm font-medium text-red-700"},o0={class:"p-4 space-y-3"},a0={class:"bg-gradient-to-r from-white to-gray-50 rounded-xl shadow-md border border-gray-200 p-4"},n0={class:"space-y-4"},i0={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},l0={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},d0={class:"bg-white rounded-lg p-4 border border-gray-200"},u0={class:"flex items-center justify-between"},c0={class:"flex items-center space-x-3"},m0={class:"text-sm font-bold text-gray-900"},f0={class:"text-right"},p0={class:"text-lg font-bold text-blue-600"},v0={class:"lg:col-span-1"},g0={class:"sticky top-8 space-y-4"},h0={class:"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"},y0={class:"p-6 space-y-4"},b0={class:"space-y-4"},x0={class:"flex justify-between items-center py-3 border-b border-gray-100"},w0={class:"text-gray-700 font-semibold"},_0={class:"text-xl font-bold text-gray-900"},k0={key:0,class:"flex justify-between items-center py-3 bg-green-50 rounded-lg px-4"},C0={class:"flex items-center space-x-3"},$0={class:"text-sm font-medium text-gray-700"},S0={class:"text-sm font-bold text-green-700"},M0={key:1,class:"flex justify-between items-center py-3 bg-blue-50 rounded-lg px-4"},A0={class:"flex items-center space-x-3"},j0={class:"text-sm font-medium text-gray-700"},T0={class:"text-sm font-bold text-blue-700"},I0={key:2,class:"flex justify-between items-center py-3 bg-red-50 rounded-lg px-4"},B0={class:"flex items-center space-x-3"},E0={class:"text-sm font-medium text-gray-700"},N0={class:"text-sm font-bold text-red-700"},D0={class:"bg-gradient-to-r from-gray-900 to-gray-800 rounded-xl p-6 text-white"},P0={class:"flex justify-between items-center"},R0={class:"text-2xl font-bold"},L0={key:0,class:"w-6 h-6 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},z0=pe({__name:"Cart",setup(t){const s=Le(),r=Be(),o=P(!1),a=P(!1),n=q(()=>s.isEmpty?"Your Cart is Empty":"Shopping Cart"),u=q(()=>s.cashItems.reduce((y,f)=>y+s.getItemSubtotal(f),0)),b=q(()=>s.onlineItems.reduce((y,f)=>y+s.getItemSubtotal(f),0)),w=q(()=>s.liveItems.reduce((y,f)=>y+s.getItemSubtotal(f),0)),l=async(y,f)=>{if(f<=0){d(y);return}try{await s.updateQuantity(y.id,f)?(r.success("Quantity updated"),s.trackCartEvent("quantity_updated",{item_id:y.id,item_name:y.name,new_quantity:f,item_price:y.target_amount})):r.error(s.error||"Failed to update item quantity")}catch(_){console.error("Error updating quantity:",_),r.error("Failed to update item quantity")}},d=async y=>{try{await s.removeFromCart(y)?(r.success(`${y.name} removed from cart`),s.trackCartEvent("item_removed",{item_id:y.id,item_name:y.name,item_price:y.target_amount,quantity:y.quantity||1})):r.error(s.error||"Failed to remove item from cart")}catch(f){console.error("Error removing item:",f),r.error("Failed to remove item from cart")}},I=async()=>{if(s.isEmpty){r.warning("Your cart is already empty");return}const y=s.cartCount,f=`Are you sure you want to remove all ${y} ${y===1?"item":"items"} from your cart? This action cannot be undone.`;if(confirm(f))try{await s.clearCart()?(r.success(`Cart cleared successfully! ${y} ${y===1?"item":"items"} removed.`),s.trackCartEvent("cart_cleared",{items_count:y,cart_total:s.cartTotal})):r.error(s.error||"Failed to clear cart")}catch(_){console.error("Error clearing cart:",_),r.error("Failed to clear cart")}},B=()=>{try{r.info("Returning to shopping...");const y=s.getLastShoppingPage();let f="/home-vue";y&&y!==window.location.href?(f=y,console.log("Returning to last shopping page:",f)):console.log("No last shopping page found, going to homepage"),setTimeout(()=>{window.location.href=f},500)}catch(y){console.error("Error in handleContinueShopping:",y),window.location.href="/home-vue"}},T=async()=>{if(s.isEmpty){r.warning("Your cart is empty. Add some items before proceeding to checkout.");return}if(s.cartTotal<=0){r.warning("Cart total must be greater than zero to proceed.");return}o.value=!0;try{r.info("Validating cart items...");const y=await s.validateCartItems();if(!y.valid&&(y.issues.forEach(f=>{r.warning(f)}),s.isEmpty)){r.error("Your cart is now empty. Please add items before proceeding."),o.value=!1;return}r.success(`Proceeding to checkout with ${s.cartCount} ${s.cartCount===1?"item":"items"}...`),s.trackCartEvent("checkout_initiated",{items_count:s.cartCount,cart_total:s.cartTotal}),await new Promise(f=>setTimeout(f,500)),window.location.href="/checkout"}catch(y){console.error("Checkout error:",y),r.error("Failed to proceed to checkout. Please try again."),o.value=!1}};return Te(async()=>{const y=window.cartItems;if(s.initializeCart(y),s.trackCartEvent("cart_page_viewed",{items_count:s.cartCount,cart_total:s.cartTotal}),!s.isEmpty&&s.isOnline)try{const f=await s.validateCartItems();!f.valid&&f.issues.length>0&&f.issues.forEach(_=>{r.warning(_)})}catch(f){console.warn("Cart validation failed on mount:",f)}}),(y,f)=>(c(),p("div",dc,[e("div",uc,[f[5]||(f[5]=ze('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5" data-v-06704d34></div><div class="absolute inset-0" data-v-06704d34><div class="absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md" data-v-06704d34></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg" data-v-06704d34></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl" data-v-06704d34></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;" data-v-06704d34></div></div>',2)),D(h($e),{class:"relative z-10"},{default:H(()=>[e("div",cc,[e("div",mc,[e("div",fc,[f[2]||(f[2]=e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500/30 to-indigo-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-blue-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])],-1)),e("div",null,[e("h1",pc,S(n.value),1),f[1]||(f[1]=e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-slate-300"},[e("li",null,[e("a",{href:"/",class:"hover:text-blue-200 transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Shopping Cart")])],-1))])]),h(s).isEmpty?z("",!0):(c(),p("div",vc,[D(h(se),{variant:"outline",size:"sm",onClick:B,class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:H(()=>f[3]||(f[3]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Continue Shopping ")])),_:1,__:[3]}),D(h(se),{size:"sm",onClick:T,loading:o.value,class:"!bg-gradient-to-r !from-blue-500 !to-indigo-600 !text-white hover:!from-blue-600 hover:!to-indigo-700 font-semibold shadow-lg hover:shadow-xl text-sm transition-all duration-200 border-0"},{default:H(()=>[o.value?z("",!0):(c(),p("svg",gc,f[4]||(f[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))),W(" "+S(o.value?"Processing...":"Checkout"),1)]),_:1},8,["loading"])]))])])]),_:1})]),D(h($e),{class:"py-12"},{default:H(()=>[h(s).loading?(c(),p("div",hc,[e("div",yc,[D(h(Ue),{size:"lg"}),f[6]||(f[6]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading your cart...",-1))])])):h(s).isEmpty?(c(),p("div",bc,[e("div",xc,[e("div",wc,[e("div",_c,[f[8]||(f[8]=e("div",{class:"relative inline-block mb-8"},[e("div",{class:"w-32 h-32 bg-gradient-to-br from-blue-100 via-blue-200 to-blue-300 rounded-full flex items-center justify-center mx-auto shadow-lg"},[e("svg",{class:"w-16 h-16 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])]),e("div",{class:"absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-bounce"}),e("div",{class:"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 rounded-full animate-pulse"})],-1)),f[9]||(f[9]=e("h3",{class:"text-3xl font-bold text-gray-900 mb-4"},"Your cart is waiting for treasures!",-1)),f[10]||(f[10]=e("p",{class:"text-lg text-gray-600 mb-8 max-w-2xl mx-auto"}," Discover amazing auction items and start building your collection. From vintage collectibles to modern masterpieces. ",-1)),D(h(se),{size:"lg",onClick:B,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"},{default:H(()=>f[7]||(f[7]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1),W(" Start Shopping Now ")])),_:1,__:[7]})])]),f[11]||(f[11]=e("div",{class:"grid grid-cols-1 md:grid-cols-3 gap-6"},[e("div",{class:"bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},[e("div",{class:"w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"},[e("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])]),e("h4",{class:"text-xl font-bold text-gray-900 mb-3"},"Cash Auctions"),e("p",{class:"text-gray-600 mb-4"},"Immediate purchases with instant ownership transfer"),e("div",{class:"text-sm text-green-600 font-medium"},"• Quick checkout • Instant delivery")]),e("div",{class:"bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},[e("div",{class:"w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"},[e("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"})])]),e("h4",{class:"text-xl font-bold text-gray-900 mb-3"},"Online Auctions"),e("p",{class:"text-gray-600 mb-4"},"Bid from anywhere, anytime with our online platform"),e("div",{class:"text-sm text-blue-600 font-medium"},"• Remote bidding • Extended timeframes")]),e("div",{class:"bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},[e("div",{class:"w-16 h-16 bg-gradient-to-br from-red-400 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"},[e("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])]),e("h4",{class:"text-xl font-bold text-gray-900 mb-3"},"Live Auctions"),e("p",{class:"text-gray-600 mb-4"},"Experience the thrill of real-time competitive bidding"),e("div",{class:"text-sm text-red-600 font-medium"},"• Real-time bidding • Live auctioneer")])],-1))])])):(c(),p("div",kc,[e("div",Cc,[e("div",$c,[e("div",Sc,[e("div",Mc,[e("div",Ac,[e("div",jc,[f[13]||(f[13]=e("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])],-1)),e("div",null,[f[12]||(f[12]=e("h3",{class:"text-lg font-bold text-gray-900"},"Your Items",-1)),e("p",Tc,S(h(s).cartCount)+" "+S(h(s).cartCount===1?"item":"items")+" selected",1)])]),D(h(Qe),{variant:"default",size:"sm",class:"bg-blue-100 text-blue-800 font-bold px-2 py-1"},{default:H(()=>[W(S(h(s).cartCount),1)]),_:1})]),e("div",Ic,[e("div",Bc,[f[14]||(f[14]=e("p",{class:"text-xs text-gray-600"},"Total Value",-1)),e("p",Ec,S(h(s).formatCurrency(h(s).cartTotal)),1)]),e("div",Nc,[D(h(se),{variant:"ghost",size:"sm",onClick:f[0]||(f[0]=_=>a.value=!a.value),class:"text-gray-500 hover:text-blue-600 hover:bg-blue-50 p-2 rounded-md",title:a.value?"Show unified view":"Group by auction type"},{default:H(()=>f[15]||(f[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)])),_:1,__:[15]},8,["title"]),D(h(se),{variant:"ghost",size:"sm",onClick:I,disabled:h(s).loading,class:"text-gray-500 hover:text-red-600 hover:bg-red-50 p-2 rounded-md",title:"Clear all items"},{default:H(()=>f[16]||(f[16]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)])),_:1,__:[16]},8,["disabled"])])])])]),e("div",Dc,[e("div",Pc,[(c(!0),p(me,null,fe(h(s).items,_=>(c(),ce(h(lt),{key:`all-${_.id}`,item:_,onUpdateQuantity:l,onRemove:d},null,8,["item"]))),128))])])]),a.value?(c(),p("div",Rc,[h(s).cashItems.length>0?(c(),p("div",Lc,[e("div",zc,[e("div",Oc,[e("div",Fc,[f[17]||(f[17]=e("div",{class:"w-3 h-3 bg-green-500 rounded-full"},null,-1)),f[18]||(f[18]=e("h4",{class:"text-sm font-semibold text-gray-900"},"Cash Auction",-1)),e("span",Vc,S(h(s).cashItems.length),1)]),e("span",qc,S(h(s).formatCurrency(u.value)),1)])]),e("div",Uc,[(c(!0),p(me,null,fe(h(s).cashItems,_=>(c(),ce(h(lt),{key:`cash-${_.id}`,item:_,onUpdateQuantity:l,onRemove:d},null,8,["item"]))),128))])])):z("",!0),h(s).onlineItems.length>0?(c(),p("div",Hc,[e("div",Wc,[e("div",Kc,[e("div",Yc,[f[19]||(f[19]=e("div",{class:"w-3 h-3 bg-blue-500 rounded-full"},null,-1)),f[20]||(f[20]=e("h4",{class:"text-sm font-semibold text-gray-900"},"Online Auction",-1)),e("span",Qc,S(h(s).onlineItems.length),1)]),e("span",Xc,S(h(s).formatCurrency(b.value)),1)])]),e("div",Gc,[(c(!0),p(me,null,fe(h(s).onlineItems,_=>(c(),ce(h(lt),{key:`online-${_.id}`,item:_,onUpdateQuantity:l,onRemove:d},null,8,["item"]))),128))])])):z("",!0),h(s).liveItems.length>0?(c(),p("div",Jc,[e("div",Zc,[e("div",e0,[e("div",t0,[f[21]||(f[21]=e("div",{class:"w-3 h-3 bg-red-500 rounded-full"},null,-1)),f[22]||(f[22]=e("h4",{class:"text-sm font-semibold text-gray-900"},"Live Auction",-1)),e("span",s0,S(h(s).liveItems.length),1)]),e("span",r0,S(h(s).formatCurrency(w.value)),1)])]),e("div",o0,[(c(!0),p(me,null,fe(h(s).liveItems,_=>(c(),ce(h(lt),{key:`live-${_.id}`,item:_,onUpdateQuantity:l,onRemove:d},null,8,["item"]))),128))])])):z("",!0)])):z("",!0),e("div",a0,[e("div",n0,[e("div",i0,[D(h(se),{variant:"outline",onClick:I,disabled:h(s).loading||h(s).isEmpty,class:"border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 font-medium py-2 px-4 rounded-lg transition-all duration-200"},{default:H(()=>f[23]||(f[23]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),W(" Clear Cart ")])),_:1,__:[23]},8,["disabled"]),D(h(se),{variant:"outline",onClick:B,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium py-2 px-4 rounded-lg transition-all duration-200"},{default:H(()=>f[24]||(f[24]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Continue Shopping ")])),_:1,__:[24]}),D(h(se),{onClick:T,loading:o.value,disabled:h(s).isEmpty,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:H(()=>[o.value?z("",!0):(c(),p("svg",l0,f[25]||(f[25]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))),W(" "+S(o.value?"Processing...":"Proceed to Checkout"),1)]),_:1},8,["loading","disabled"])]),e("div",d0,[e("div",u0,[e("div",c0,[f[27]||(f[27]=e("div",{class:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])],-1)),e("div",null,[f[26]||(f[26]=e("p",{class:"text-xs text-gray-600"},"Items in Cart",-1)),e("p",m0,S(h(s).cartCount)+" "+S(h(s).cartCount===1?"item":"items"),1)])]),e("div",f0,[f[28]||(f[28]=e("p",{class:"text-xs text-gray-600"},"Total Value",-1)),e("p",p0,S(h(s).formatCurrency(h(s).cartTotal)),1)])])])])])]),e("div",v0,[e("div",g0,[e("div",h0,[f[34]||(f[34]=e("div",{class:"px-6 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold"},"Order Summary"),e("p",{class:"text-blue-100 text-xs"},"Review your selection")])])],-1)),e("div",y0,[e("div",b0,[e("div",x0,[e("span",w0,"Items ("+S(h(s).cartCount)+")",1),e("span",_0,S(h(s).formatCurrency(h(s).cartTotal)),1)]),h(s).cashItems.length>0?(c(),p("div",k0,[e("div",C0,[f[29]||(f[29]=e("div",{class:"w-4 h-4 bg-green-500 rounded-full"},null,-1)),e("span",$0,"Cash Items ("+S(h(s).cashItems.length)+")",1)]),e("span",S0,S(h(s).formatCurrency(u.value)),1)])):z("",!0),h(s).onlineItems.length>0?(c(),p("div",M0,[e("div",A0,[f[30]||(f[30]=e("div",{class:"w-4 h-4 bg-blue-500 rounded-full"},null,-1)),e("span",j0,"Online Items ("+S(h(s).onlineItems.length)+")",1)]),e("span",T0,S(h(s).formatCurrency(b.value)),1)])):z("",!0),h(s).liveItems.length>0?(c(),p("div",I0,[e("div",B0,[f[31]||(f[31]=e("div",{class:"w-4 h-4 bg-red-500 rounded-full"},null,-1)),e("span",E0,"Live Items ("+S(h(s).liveItems.length)+")",1)]),e("span",N0,S(h(s).formatCurrency(w.value)),1)])):z("",!0)]),e("div",D0,[e("div",P0,[f[32]||(f[32]=e("span",{class:"text-lg font-bold"},"Grand Total",-1)),e("span",R0,S(h(s).formatCurrency(h(s).cartTotal)),1)])]),D(h(se),{size:"lg",class:"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-4 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300",loading:o.value,onClick:T},{default:H(()=>[o.value?z("",!0):(c(),p("svg",L0,f[33]||(f[33]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))),W(" "+S(o.value?"Processing...":"Proceed to Checkout"),1)]),_:1},8,["loading"])])]),f[35]||(f[35]=e("div",{class:"bg-white rounded-2xl shadow-lg border border-gray-100 p-6"},[e("h4",{class:"font-bold text-gray-900 mb-4 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),W(" Secure & Trusted ")]),e("div",{class:"space-y-3"},[e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" SSL encrypted checkout process ")])])],-1))])])]))]),_:1})]))}});const Rt=Ae(z0,[["__scopeId","data-v-06704d34"]]),O0={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},F0={class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},V0={class:"py-6"},q0={class:"flex items-center justify-between"},U0={class:"flex items-center space-x-3"},H0={key:0,class:"flex justify-center py-20"},W0={class:"text-center"},K0={key:1,class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Y0={class:"lg:col-span-2 space-y-6"},Q0={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},X0={class:"p-6"},G0={class:"flex items-center justify-between"},J0={class:"flex items-center"},Z0={key:0,class:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 20 20"},em={key:1},tm={class:"ml-3"},sm={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},rm={key:0,class:"p-6"},om={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},am={class:"flex justify-end space-x-3"},nm={key:1,class:"p-6"},im={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},lm={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},dm={class:"flex justify-between space-x-3"},um={key:2,class:"p-6"},cm={class:"space-y-4"},mm={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},fm=["onClick"],pm={class:"flex items-center space-x-3"},vm={key:0,class:"w-2 h-2 bg-white rounded-full m-0.5"},gm={class:"flex items-center space-x-2"},hm={key:0,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ym={key:1,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},bm={key:2,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},xm={key:3,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},wm={key:4,class:"ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full"},_m={key:0,class:"space-y-4 p-4 bg-blue-50 rounded-lg"},km={class:"bg-white rounded-lg p-4 border border-blue-200"},Cm={class:"bg-gray-50 rounded-lg p-3 border"},$m={class:"flex justify-between items-center"},Sm={key:0,class:"mt-2 p-2 bg-red-50 rounded border border-red-200"},Mm={key:1,class:"space-y-4 p-4 bg-gray-50 rounded-lg"},Am={class:"grid grid-cols-1 gap-4"},jm={class:"grid grid-cols-2 gap-4"},Tm={key:0,class:"space-y-2"},Im={key:0,class:"flex items-center space-x-2 text-sm"},Bm={key:0,class:"w-4 h-4 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},Em={key:1,class:"flex items-center space-x-2 text-xs text-gray-500"},Nm={class:"flex-1 bg-gray-200 rounded-full h-1 ml-2"},Dm={key:2,class:"space-y-4 p-4 bg-gradient-to-br from-green-50 to-blue-50 rounded-lg border border-green-200"},Pm={class:"grid grid-cols-1 gap-4"},Rm={class:"grid grid-cols-2 gap-4"},Lm={class:"flex justify-between space-x-3"},zm={key:3,class:"p-6"},Om={class:"space-y-6"},Fm={class:"bg-gray-50 rounded-lg p-4"},Vm={class:"space-y-3"},qm={class:"flex justify-between"},Um={class:"text-gray-600"},Hm={class:"font-medium"},Wm={class:"flex justify-between"},Km={class:"font-medium"},Ym={class:"border-t pt-3 flex justify-between"},Qm={class:"text-lg font-bold text-gray-900"},Xm={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Gm={class:"bg-gray-50 rounded-lg p-4"},Jm={class:"space-y-2 text-sm"},Zm={class:"bg-gray-50 rounded-lg p-4"},e1={class:"space-y-2 text-sm"},t1={class:"bg-gray-50 rounded-lg p-4"},s1={class:"text-sm"},r1={key:0,class:"text-gray-600"},o1={key:1,class:"text-gray-600"},a1={key:2,class:"text-gray-600"},n1={key:3,class:"text-gray-600"},i1={key:0,class:"p-4 bg-blue-50 border border-blue-200 rounded-lg"},l1={class:"flex items-center space-x-3"},d1={class:"flex-1"},u1={class:"text-sm text-blue-700"},c1={key:1,class:"p-4 bg-red-50 border border-red-200 rounded-lg"},m1={class:"flex items-start space-x-3"},f1={class:"flex-1"},p1={class:"text-sm text-red-700"},v1={class:"flex items-start space-x-3"},g1={class:"flex justify-between space-x-3"},h1={key:0},y1={key:1},b1={key:2},x1={class:"lg:col-span-1"},w1={class:"sticky top-8 space-y-4"},_1={class:"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"},k1={class:"px-6 py-4 bg-gradient-to-r from-green-600 to-green-700 text-white"},C1={class:"flex items-center space-x-3"},$1={class:"text-green-100 text-xs"},S1={class:"p-6 space-y-4"},M1={class:"space-y-3 max-h-64 overflow-y-auto"},A1={class:"flex-1 min-w-0"},j1={class:"text-sm font-medium text-gray-900 truncate"},T1={class:"text-xs text-gray-500"},I1={class:"text-sm font-medium text-gray-900"},B1={class:"space-y-3 pt-4 border-t border-gray-200"},E1={class:"flex justify-between text-sm"},N1={class:"font-medium"},D1={class:"flex justify-between text-sm"},P1={class:"font-medium"},R1={class:"flex justify-between text-lg font-bold pt-3 border-t border-gray-200"},L1={class:"text-green-600"},z1=pe({__name:"Checkout",setup(t){const s=Le(),r=Be(),o=P(!1),a=P(!1),n=P(!1),u=P(!1),b=P(!1),w=P(""),l=P(""),d=P("customer"),I=P(!1),B=P(""),T=P(""),y=P(!1),f=P([{id:"customer",title:"Customer Info",description:"Contact details",active:!0,completed:!1},{id:"billing",title:"Billing Address",description:"Billing information",active:!1,completed:!1},{id:"payment",title:"Payment",description:"Payment method",active:!1,completed:!1},{id:"review",title:"Review",description:"Confirm order",active:!1,completed:!1}]),_=P({firstName:"",lastName:"",email:"",phone:""}),R=P({firstName:"",lastName:"",email:"",phone:""}),M=P({address:"",city:"",state:"",postalCode:"",country:""}),g=P({address:"",city:"",state:"",postalCode:"",country:""}),v=P({method:"dpopay",cardNumber:"",expiryDate:"",cvv:"",cardName:"",customerName:"",customerEmail:"",customerPhone:"",currency:"MWK"}),x=P({cardNumber:"",expiryDate:"",cvv:"",cardName:"",customerName:"",customerEmail:"",customerPhone:""}),j=P([{id:"dpopay",name:"DPO Pay",description:"Pay with DPO Pay - Credit Cards, Mobile Money & Bank Transfer",enabled:!0,icon:"credit-card",currencies:["MWK","USD","EUR","GBP","ZAR"]},{id:"cash",name:"Cash on Delivery",description:"Pay when you receive your items",enabled:!0},{id:"bank",name:"Bank Transfer",description:"Direct bank transfer (Coming Soon)",enabled:!1},{id:"card",name:"Credit/Debit Card",description:"Pay securely with your credit or debit card (Coming Soon)",enabled:!1}]),A=(m,i)=>m.completed?"bg-green-500 text-white":m.active?"bg-blue-500 text-white":"bg-gray-200 text-gray-500",O=()=>{window.location.href="/cart"},Z=()=>{R.value={firstName:"",lastName:"",email:"",phone:""};let m=!1;if(_.value.firstName.trim()||(R.value.firstName="First name is required",m=!0),_.value.lastName.trim()||(R.value.lastName="Last name is required",m=!0),_.value.email.trim()?/\S+@\S+\.\S+/.test(_.value.email)||(R.value.email="Please enter a valid email address",m=!0):(R.value.email="Email is required",m=!0),_.value.phone.trim()||(R.value.phone="Phone number is required",m=!0),m){r.error("Please fix the errors below");return}a.value=!0,setTimeout(()=>{L(),a.value=!1,r.success("Customer information saved successfully")},1e3)},oe=()=>{g.value={address:"",city:"",state:"",postalCode:"",country:""};let m=!1;if(M.value.address.trim()||(g.value.address="Street address is required",m=!0),M.value.city.trim()||(g.value.city="City is required",m=!0),M.value.state.trim()||(g.value.state="State/Province is required",m=!0),M.value.postalCode.trim()||(g.value.postalCode="Postal code is required",m=!0),M.value.country.trim()||(g.value.country="Country is required",m=!0),m){r.error("Please fix the errors below");return}n.value=!0,setTimeout(()=>{L(),n.value=!1,r.success("Billing address saved successfully")},1e3)},de=()=>{x.value={cardNumber:"",expiryDate:"",cvv:"",cardName:"",customerName:"",customerEmail:"",customerPhone:""};let m=!1;if(v.value.method==="dpopay")v.value.customerName.trim()?v.value.customerName.trim().length<2&&(x.value.customerName="Please enter a valid name",m=!0):(x.value.customerName="Full name is required",m=!0),v.value.customerEmail.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v.value.customerEmail)||(x.value.customerEmail="Please enter a valid email address",m=!0):(x.value.customerEmail="Email address is required",m=!0),v.value.customerPhone.trim()&&(/^[\+]?[0-9\-\(\)\s]+$/.test(v.value.customerPhone)||(x.value.customerPhone="Please enter a valid phone number",m=!0));else if(v.value.method==="card"){if(v.value.cardNumber.replace(/\s/g,"")?y.value||(x.value.cardNumber||(x.value.cardNumber="Please enter a valid card number"),m=!0):(x.value.cardNumber="Card number is required",m=!0),!v.value.expiryDate.trim())x.value.expiryDate="Expiry date is required",m=!0;else if(v.value.expiryDate.length!==5)x.value.expiryDate="Please enter a valid expiry date (MM/YY)",m=!0;else{const[K,N]=v.value.expiryDate.split("/"),G=new Date,ie=G.getFullYear()%100,le=G.getMonth()+1,re=parseInt(K),we=parseInt(N);re<1||re>12?(x.value.expiryDate="Invalid month",m=!0):(we<ie||we===ie&&re<le)&&(x.value.expiryDate="Card has expired",m=!0)}if(!v.value.cvv.trim())x.value.cvv="CVV is required",m=!0;else{const K=B.value==="American Express"?4:3;v.value.cvv.length!==K&&(x.value.cvv=`CVV must be ${K} digits`,m=!0)}v.value.cardName.trim()?v.value.cardName.trim().length<2&&(x.value.cardName="Please enter a valid name",m=!0):(x.value.cardName="Name on card is required",m=!0)}else v.value.method;if(m){r.error("Please fix the errors below");return}u.value=!0,setTimeout(()=>{L(),u.value=!1,r.success("Payment method saved successfully")},1e3)},te=m=>{let i=0,K=!1;for(let N=m.length-1;N>=0;N--){let G=parseInt(m.charAt(N));K&&(G*=2,G>9&&(G-=9)),i+=G,K=!K}return i%10===0},V=async()=>{if(w.value="",l.value="",!I.value){const m="Please agree to the terms and conditions to continue";w.value=m,r.error(m);return}b.value=!0,l.value="Initializing order...";try{if(v.value.method==="dpopay")l.value="Preparing DPO Pay transaction...",await Y();else if(v.value.method==="cash")l.value="Processing cash on delivery order...",await ne();else{const m="Selected payment method is not available yet";w.value=m,r.error(m),b.value=!1,l.value=""}}catch(m){console.error("Order placement error:",m);let i="Failed to place order. Please try again.";m.message&&(i=m.message),w.value=i,r.error(i),b.value=!1,l.value=""}},Y=async()=>{var m;try{if(s.isEmpty)throw new Error("Your cart is empty. Please add items before checkout.");if(s.cartTotal<=0)throw new Error("Invalid cart total. Please refresh and try again.");if(s.cartTotal>999999.99)throw new Error("Cart total exceeds DPO Pay maximum limit of MK 999,999.99. Please reduce your order or contact support for large transactions.");const i={customer:{firstName:_.value.firstName,lastName:_.value.lastName,email:_.value.email,phone:_.value.phone},billing:{address:M.value.address,city:M.value.city,state:M.value.state,postalCode:M.value.postalCode,country:M.value.country},cart:{items:s.items,total:s.cartTotal},currency:"MWK"};l.value="Creating secure payment token...",r.info("Creating secure payment token...");const K=await fetch("/dpopay/checkout-payment",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":((m=document.querySelector('meta[name="csrf-token"]'))==null?void 0:m.getAttribute("content"))||"",Accept:"application/json"},body:JSON.stringify(i)});if(!K.ok){let G="Payment processing failed. Please try again.",ie=null,le=null;try{const re=await K.json();re.message&&(G=re.message),ie=re.error_code,le=re.technical_error||re.error,(ie||le)&&console.error("DPO Pay API Error:",{code:ie,message:G,technical:le})}catch{try{const we=await K.text();console.error("DPO Pay Raw Error Response:",we),we.includes("Payment amount cannot exceed")?G="Cart total exceeds DPO Pay maximum limit of MK 999,999.99. Please reduce your order or contact support for large transactions.":we.includes("Invalid company token")?G="Payment gateway configuration error. Please contact support.":we.includes("Service Type not active")?G="Payment service temporarily unavailable. Please try again later.":we&&(G="Payment processing error. Please try again or contact support.")}catch{G=`Connection error (${K.status}). Please check your internet connection and try again.`}}throw new Error(G)}const N=await K.json();if(N.success&&N.payment_url){l.value="Payment token created! Redirecting to DPO Pay...",r.success("Payment token created! Redirecting to DPO Pay..."),localStorage.setItem("vertigo_order_ref",N.order_reference),localStorage.setItem("vertigo_transaction_token",N.transaction_token),localStorage.setItem("vertigo_payment_amount",s.cartTotal.toString()),localStorage.setItem("vertigo_payment_currency",v.value.currency);let G=3;const ie=setInterval(()=>{l.value=`Redirecting to DPO Pay in ${G} seconds...`,G--,G<0&&(clearInterval(ie),l.value="Redirecting to payment gateway...",window.location.href=N.payment_url)},1e3)}else{let G=N.message||"Failed to create payment token";throw(N.error_code||N.technical_error)&&console.error("DPO Pay API Error Details:",{code:N.error_code,message:G,technical:N.technical_error}),new Error(G)}}catch(i){console.error("DPO Payment error:",i);let K="Failed to process DPO payment. Please try again.";i.message.includes("Cart total exceeds")?K=i.message:i.message.includes("HTTP error")?K="Unable to connect to payment gateway. Please check your internet connection and try again.":i.message.includes("cart is empty")?K="Your cart is empty. Please add items before proceeding to checkout.":i.message.includes("Invalid cart total")?K="There was an issue with your cart total. Please refresh the page and try again.":i.message.includes("Failed to create payment token")&&(K="Payment token creation failed. This may be a temporary issue with the payment gateway. Please try again in a few moments."),w.value=K,r.error(K),b.value=!1,l.value=""}},ne=async()=>{try{r.info("Processing cash on delivery order..."),await new Promise(m=>setTimeout(m,2e3)),s.clearCart(),r.success("Order placed successfully! You will pay on delivery."),setTimeout(()=>{window.location.href="/"},2e3),b.value=!1}catch(m){console.error("Cash on delivery error:",m),r.error("Failed to process cash on delivery order"),b.value=!1}},L=()=>{const m=f.value.findIndex(i=>i.id===d.value);f.value[m].completed=!0,f.value[m].active=!1,m+1<f.value.length&&(f.value[m+1].active=!0,d.value=f.value[m+1].id)},$=()=>{const m=f.value.findIndex(i=>i.id===d.value);m>0&&(f.value[m].active=!1,f.value[m-1].active=!0,f.value[m-1].completed=!1,d.value=f.value[m-1].id)},F=()=>j.value.find(m=>m.id===v.value.method),U=()=>{w.value="",l.value=""},Q=m=>{const i=m.replace(/\s/g,"");return/^4/.test(i)?"Visa":/^5[1-5]/.test(i)||/^2[2-7]/.test(i)?"Mastercard":/^3[47]/.test(i)?"American Express":/^6/.test(i)?"Discover":""},ee=m=>{switch(m){case"American Express":return{minLength:15,maxLength:15,formatted:18};case"Visa":case"Mastercard":case"Discover":return{minLength:16,maxLength:16,formatted:19};default:return{minLength:13,maxLength:19,formatted:23}}},ue=()=>ee(B.value).formatted,xe=m=>{let i=m.target.value.replace(/\s/g,"");const K=Q(i);B.value=K;const N=ee(K);i.length>N.maxLength&&(i=i.substring(0,N.maxLength));let G="";if(K==="American Express")for(let ie=0;ie<i.length;ie++)(ie===4||ie===10)&&(G+=" "),G+=i[ie];else for(let ie=0;ie<i.length;ie++)ie>0&&ie%4===0&&(G+=" "),G+=i[ie];v.value.cardNumber=G,Re(i)},Re=m=>{const i=ee(B.value);if(x.value.cardNumber="",T.value="",y.value=!1,m.length!==0){if(m.length<i.minLength){m.length>=4&&(x.value.cardNumber=`Card number must be ${i.minLength} digits`);return}if(B.value&&m.length!==i.maxLength){x.value.cardNumber=`${B.value} cards must be ${i.maxLength} digits`;return}if(!B.value&&m.length>16){x.value.cardNumber="Card number is too long";return}m.length>=i.minLength&&(te(m)?(T.value=`Valid ${B.value||"card"} number`,y.value=!0,x.value.cardNumber=""):(x.value.cardNumber="Invalid card number",y.value=!1))}},Ne=m=>{const i=m.target.value.replace(/\s/g,""),K=ee(B.value);if(!([8,9,27,13,46,37,38,39,40].indexOf(m.keyCode)!==-1||m.keyCode===65&&m.ctrlKey===!0||m.keyCode===67&&m.ctrlKey===!0||m.keyCode===86&&m.ctrlKey===!0||m.keyCode===88&&m.ctrlKey===!0||m.keyCode===90&&m.ctrlKey===!0)){if(i.length>=K.maxLength){m.preventDefault();return}(m.shiftKey||m.keyCode<48||m.keyCode>57)&&(m.keyCode<96||m.keyCode>105)&&m.preventDefault()}},je=m=>{let i=m.target.value.replace(/\D/g,"");if(i.length>=2&&(i=i.substring(0,2)+"/"+i.substring(2,4)),v.value.expiryDate=i,i.length===5){const[K,N]=i.split("/"),G=new Date,ie=G.getFullYear()%100,le=G.getMonth()+1,re=parseInt(K),we=parseInt(N);re<1||re>12?x.value.expiryDate="Invalid month":we<ie||we===ie&&re<le?x.value.expiryDate="Card has expired":x.value.expiryDate=""}},Oe=m=>{[8,9,27,13,46].indexOf(m.keyCode)!==-1||m.keyCode===65&&m.ctrlKey===!0||m.keyCode===67&&m.ctrlKey===!0||m.keyCode===86&&m.ctrlKey===!0||m.keyCode===88&&m.ctrlKey===!0||(m.shiftKey||m.keyCode<48||m.keyCode>57)&&(m.keyCode<96||m.keyCode>105)&&m.preventDefault()},C=m=>{let i=m.target.value.replace(/\D/g,"");const K=B.value==="American Express"?4:3;i=i.substring(0,K),v.value.cvv=i,i.length>=3&&(x.value.cvv="")},k=m=>{[8,9,27,13,46].indexOf(m.keyCode)!==-1||m.keyCode===65&&m.ctrlKey===!0||m.keyCode===67&&m.ctrlKey===!0||m.keyCode===86&&m.ctrlKey===!0||m.keyCode===88&&m.ctrlKey===!0||(m.shiftKey||m.keyCode<48||m.keyCode>57)&&(m.keyCode<96||m.keyCode>105)&&m.preventDefault()},X=m=>{let i=m.target.value.replace(/[^a-zA-Z\s\-']/g,"");i=i.replace(/\b\w/g,K=>K.toUpperCase()),v.value.cardName=i,i.trim().length>0&&(x.value.cardName="")};return Te(()=>{const m=window.cartItems||[];if(s.initializeCart(m),s.isEmpty&&d.value==="customer"&&(r.warning("Your cart is empty. Redirecting to homepage..."),setTimeout(()=>{window.location.href="/"},2e3)),v.value.cardNumber){const i=v.value.cardNumber.replace(/\s/g,"");Re(i)}}),(m,i)=>(c(),p("div",O0,[e("div",F0,[i[20]||(i[20]=ze('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5" data-v-a7a2dac4></div><div class="absolute inset-0" data-v-a7a2dac4><div class="absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md" data-v-a7a2dac4></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg" data-v-a7a2dac4></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl" data-v-a7a2dac4></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;" data-v-a7a2dac4></div></div>',2)),D(h($e),{class:"relative z-10"},{default:H(()=>[e("div",V0,[e("div",q0,[i[19]||(i[19]=e("div",{class:"flex items-center space-x-4"},[e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-500/30 to-emerald-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-green-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Secure Checkout "),e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-slate-300"},[e("li",null,[e("a",{href:"/",class:"hover:text-blue-200 transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",null,[e("a",{href:"/cart",class:"hover:text-blue-200 transition-colors duration-200"},"Cart")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Checkout")])])])],-1)),e("div",U0,[D(h(se),{variant:"outline",size:"sm",onClick:O,class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:H(()=>i[18]||(i[18]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Back to Cart ")])),_:1,__:[18]})])])])]),_:1})]),D(h($e),{class:"py-12"},{default:H(()=>{var K;return[o.value?(c(),p("div",H0,[e("div",W0,[D(h(Ue),{size:"lg"}),i[21]||(i[21]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading checkout...",-1))])])):(c(),p("div",K0,[e("div",Y0,[e("div",Q0,[i[23]||(i[23]=e("div",{class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},[e("h3",{class:"text-lg font-bold text-gray-900"},"Checkout Progress")],-1)),e("div",X0,[e("div",G0,[(c(!0),p(me,null,fe(f.value,(N,G)=>(c(),p("div",{key:N.id,class:J(["flex items-center",{"flex-1":G<f.value.length-1}])},[e("div",J0,[e("div",{class:J(["w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300",A(N,G)])},[N.completed?(c(),p("svg",Z0,i[22]||(i[22]=[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"},null,-1)]))):(c(),p("span",em,S(G+1),1))],2),e("div",tm,[e("p",{class:J(["text-sm font-medium",N.active||N.completed?"text-gray-900":"text-gray-500"])},S(N.title),3),e("p",{class:J(["text-xs",N.active||N.completed?"text-gray-600":"text-gray-400"])},S(N.description),3)])]),G<f.value.length-1?(c(),p("div",{key:0,class:J(["flex-1 h-0.5 mx-4 transition-all duration-300",f.value[G+1].completed||f.value[G+1].active?"bg-green-500":"bg-gray-200"])},null,2)):z("",!0)],2))),128))])])]),e("div",sm,[d.value==="customer"?(c(),p("div",rm,[i[25]||(i[25]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Customer Information"),e("p",{class:"text-sm text-gray-600"},"Please provide your contact details")])],-1)),e("form",{onSubmit:Ce(Z,["prevent"]),class:"space-y-6"},[e("div",om,[D(h(be),{modelValue:_.value.firstName,"onUpdate:modelValue":i[0]||(i[0]=N=>_.value.firstName=N),label:"First Name",placeholder:"Enter your first name",required:"",error:R.value.firstName},null,8,["modelValue","error"]),D(h(be),{modelValue:_.value.lastName,"onUpdate:modelValue":i[1]||(i[1]=N=>_.value.lastName=N),label:"Last Name",placeholder:"Enter your last name",required:"",error:R.value.lastName},null,8,["modelValue","error"])]),D(h(be),{modelValue:_.value.email,"onUpdate:modelValue":i[2]||(i[2]=N=>_.value.email=N),type:"email",label:"Email Address",placeholder:"Enter your email address",required:"",error:R.value.email},null,8,["modelValue","error"]),D(h(be),{modelValue:_.value.phone,"onUpdate:modelValue":i[3]||(i[3]=N=>_.value.phone=N),type:"tel",label:"Phone Number",placeholder:"Enter your phone number",required:"",error:R.value.phone},null,8,["modelValue","error"]),e("div",am,[D(h(se),{type:"submit",loading:a.value,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:H(()=>i[24]||(i[24]=[W(" Continue to Billing ")])),_:1,__:[24]},8,["loading"])])],32)])):z("",!0),d.value==="billing"?(c(),p("div",nm,[i[28]||(i[28]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Billing Address"),e("p",{class:"text-sm text-gray-600"},"Where should we send your invoice?")])],-1)),e("form",{onSubmit:Ce(oe,["prevent"]),class:"space-y-6"},[D(h(be),{modelValue:M.value.address,"onUpdate:modelValue":i[4]||(i[4]=N=>M.value.address=N),label:"Street Address",placeholder:"Enter your street address",required:"",error:g.value.address},null,8,["modelValue","error"]),e("div",im,[D(h(be),{modelValue:M.value.city,"onUpdate:modelValue":i[5]||(i[5]=N=>M.value.city=N),label:"City",placeholder:"Enter your city",required:"",error:g.value.city},null,8,["modelValue","error"]),D(h(be),{modelValue:M.value.postalCode,"onUpdate:modelValue":i[6]||(i[6]=N=>M.value.postalCode=N),label:"Postal Code",placeholder:"Enter postal code",required:"",error:g.value.postalCode},null,8,["modelValue","error"])]),e("div",lm,[D(h(be),{modelValue:M.value.state,"onUpdate:modelValue":i[7]||(i[7]=N=>M.value.state=N),label:"State/Province",placeholder:"Enter state or province",required:"",error:g.value.state},null,8,["modelValue","error"]),D(h(be),{modelValue:M.value.country,"onUpdate:modelValue":i[8]||(i[8]=N=>M.value.country=N),label:"Country",placeholder:"Enter country",required:"",error:g.value.country},null,8,["modelValue","error"])]),e("div",dm,[D(h(se),{type:"button",variant:"outline",onClick:$,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium px-6 py-2 rounded-lg transition-all duration-200"},{default:H(()=>i[26]||(i[26]=[W(" Back to Customer Info ")])),_:1,__:[26]}),D(h(se),{type:"submit",loading:n.value,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:H(()=>i[27]||(i[27]=[W(" Continue to Payment ")])),_:1,__:[27]},8,["loading"])])],32)])):z("",!0),d.value==="payment"?(c(),p("div",um,[i[51]||(i[51]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Payment Method"),e("p",{class:"text-sm text-gray-600"},"Choose how you'd like to pay")])],-1)),e("form",{onSubmit:Ce(de,["prevent"]),class:"space-y-6"},[e("div",cm,[e("div",mm,[(c(!0),p(me,null,fe(j.value,N=>(c(),p("div",{key:N.id,onClick:G=>N.enabled&&(v.value.method=N.id),class:J(["border-2 rounded-lg p-4 transition-all duration-200",N.enabled?"cursor-pointer":"cursor-not-allowed opacity-60",v.value.method===N.id&&N.enabled?"border-blue-500 bg-blue-50":N.enabled?"border-gray-200 hover:border-gray-300":"border-gray-200 bg-gray-50"])},[e("div",pm,[e("div",{class:J(["w-4 h-4 rounded-full border-2 transition-all duration-200",v.value.method===N.id?"border-blue-500 bg-blue-500":"border-gray-300"])},[v.value.method===N.id?(c(),p("div",vm)):z("",!0)],2),e("div",gm,[N.id==="dpopay"?(c(),p("svg",hm,i[29]||(i[29]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"},null,-1)]))):N.id==="cash"?(c(),p("svg",ym,i[30]||(i[30]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z"},null,-1)]))):N.id==="bank"?(c(),p("svg",bm,i[31]||(i[31]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},null,-1)]))):N.id==="card"?(c(),p("svg",xm,i[32]||(i[32]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))):z("",!0),e("span",{class:J(["font-medium",N.enabled?"text-gray-900":"text-gray-500"])},S(N.name),3),N.enabled?z("",!0):(c(),p("span",wm," Coming Soon "))])]),e("p",{class:J(["text-sm mt-2 ml-7",N.enabled?"text-gray-600":"text-gray-500"])},S(N.description),3)],10,fm))),128))])]),v.value.method==="dpopay"?(c(),p("div",_m,[i[40]||(i[40]=e("h4",{class:"font-medium text-gray-900"},"DPO Payment Details",-1)),e("div",km,[i[36]||(i[36]=e("div",{class:"flex items-center space-x-2 mb-3"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})]),e("span",{class:"text-sm font-medium text-blue-900"},"Secure Payment via DPO")],-1)),i[37]||(i[37]=e("p",{class:"text-sm text-gray-600 mb-4"}," You will be redirected to DPO's secure payment gateway where you can pay using: ",-1)),i[38]||(i[38]=e("div",{class:"grid grid-cols-2 gap-3 text-xs mb-4"},[e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-green-500 rounded-full"}),e("span",null,"Mobile Money (Airtel, TNM)")]),e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-blue-500 rounded-full"}),e("span",null,"Credit/Debit Cards")]),e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-purple-500 rounded-full"}),e("span",null,"Bank Transfer")]),e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-orange-500 rounded-full"}),e("span",null,"Digital Wallets")])],-1)),e("div",Cm,[e("div",$m,[i[33]||(i[33]=e("span",{class:"text-sm text-gray-600"},"Total Amount:",-1)),e("span",{class:J(["text-lg font-bold",h(s).cartTotal>999999.99?"text-red-600":"text-gray-900"])},S(h(s).formatCurrency(h(s).cartTotal)),3)]),i[35]||(i[35]=e("div",{class:"flex justify-between items-center mt-1"},[e("span",{class:"text-xs text-gray-500"},"Currency:"),e("span",{class:"text-xs font-medium text-gray-700"},"MWK (Malawi Kwacha)")],-1)),h(s).cartTotal>999999.99?(c(),p("div",Sm,i[34]||(i[34]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-red-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})]),e("span",{class:"text-xs text-red-800"},"Amount exceeds DPO Pay limit (MK 999,999.99)")],-1)]))):z("",!0)]),i[39]||(i[39]=e("div",{class:"mt-3 p-2 bg-green-50 rounded border border-green-200"},[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),e("span",{class:"text-xs text-green-800"},"SSL encrypted and PCI compliant")])],-1))])])):v.value.method==="card"?(c(),p("div",Mm,[i[44]||(i[44]=e("h4",{class:"font-medium text-gray-900"},"Card Details",-1)),e("div",Am,[D(h(be),{modelValue:v.value.cardNumber,"onUpdate:modelValue":i[9]||(i[9]=N=>v.value.cardNumber=N),label:"Card Number",placeholder:"1234 5678 9012 3456",required:"",error:x.value.cardNumber,success:T.value,onInput:xe,onKeydown:Ne,maxlength:ue()},null,8,["modelValue","error","success","maxlength"]),e("div",jm,[D(h(be),{modelValue:v.value.expiryDate,"onUpdate:modelValue":i[10]||(i[10]=N=>v.value.expiryDate=N),label:"Expiry Date",placeholder:"MM/YY",required:"",error:x.value.expiryDate,onInput:je,onKeydown:Oe,maxlength:"5"},null,8,["modelValue","error"]),D(h(be),{modelValue:v.value.cvv,"onUpdate:modelValue":i[11]||(i[11]=N=>v.value.cvv=N),label:"CVV",placeholder:"123",required:"",error:x.value.cvv,onInput:C,onKeydown:k,maxlength:"4"},null,8,["modelValue","error"])]),D(h(be),{modelValue:v.value.cardName,"onUpdate:modelValue":i[12]||(i[12]=N=>v.value.cardName=N),label:"Name on Card",placeholder:"Enter name as shown on card",required:"",error:x.value.cardName,onInput:X},null,8,["modelValue","error"])]),B.value||v.value.cardNumber?(c(),p("div",Tm,[B.value?(c(),p("div",Im,[(c(),p("svg",{class:J(["w-5 h-5",y.value?"text-green-500":"text-blue-500"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},i[41]||(i[41]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]),2)),e("span",{class:J(y.value?"text-green-600":"text-blue-600")},S(B.value)+" detected ",3),y.value?(c(),p("svg",Bm,i[42]||(i[42]=[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):z("",!0)])):z("",!0),v.value.cardNumber?(c(),p("div",Em,[e("span",null,S(v.value.cardNumber.replace(/\s/g,"").length),1),i[43]||(i[43]=e("span",null,"/",-1)),e("span",null,S(ee(B.value).maxLength)+" digits",1),e("div",Nm,[e("div",{class:J(["h-1 rounded-full transition-all duration-300",y.value?"bg-green-500":x.value.cardNumber?"bg-red-500":"bg-blue-500"]),style:rt({width:`${Math.min(v.value.cardNumber.replace(/\s/g,"").length/ee(B.value).maxLength*100,100)}%`})},null,6)])])):z("",!0)])):z("",!0)])):z("",!0),v.value.method==="dpopay"?(c(),p("div",Dm,[i[47]||(i[47]=e("div",{class:"flex items-center space-x-3 mb-4"},[e("div",{class:"w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("div",null,[e("h4",{class:"font-medium text-gray-900"},"DPO Pay - Secure Payment Gateway"),e("p",{class:"text-sm text-gray-600"},"You'll be redirected to DPO Pay to complete your payment")])],-1)),e("div",Pm,[e("div",Rm,[D(h(be),{modelValue:v.value.customerName,"onUpdate:modelValue":i[13]||(i[13]=N=>v.value.customerName=N),label:"Full Name",placeholder:"Enter your full name",required:"",error:x.value.customerName},null,8,["modelValue","error"]),e("div",null,[i[46]||(i[46]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Currency",-1)),he(e("select",{"onUpdate:modelValue":i[14]||(i[14]=N=>v.value.currency=N),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},i[45]||(i[45]=[e("option",{value:"MWK"},"MWK - Malawi Kwacha",-1),e("option",{value:"USD"},"USD - US Dollar",-1),e("option",{value:"EUR"},"EUR - Euro",-1),e("option",{value:"GBP"},"GBP - British Pound",-1),e("option",{value:"ZAR"},"ZAR - South African Rand",-1)]),512),[[Bs,v.value.currency]])])]),D(h(be),{modelValue:v.value.customerEmail,"onUpdate:modelValue":i[15]||(i[15]=N=>v.value.customerEmail=N),label:"Email Address",type:"email",placeholder:"Enter your email address",required:"",error:x.value.customerEmail},null,8,["modelValue","error"]),D(h(be),{modelValue:v.value.customerPhone,"onUpdate:modelValue":i[16]||(i[16]=N=>v.value.customerPhone=N),label:"Phone Number",placeholder:"+265 123 456 789",error:x.value.customerPhone},null,8,["modelValue","error"])]),i[48]||(i[48]=e("div",{class:"bg-white rounded-lg p-4 border border-gray-200"},[e("h5",{class:"font-medium text-gray-900 mb-3"},"Supported Payment Methods"),e("div",{class:"grid grid-cols-3 gap-4 text-center"},[e("div",{class:"flex flex-col items-center space-y-2"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("span",{class:"text-xs text-gray-600"},"Credit Cards")]),e("div",{class:"flex flex-col items-center space-y-2"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])]),e("span",{class:"text-xs text-gray-600"},"Mobile Money")]),e("div",{class:"flex flex-col items-center space-y-2"},[e("div",{class:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])]),e("span",{class:"text-xs text-gray-600"},"Bank Transfer")])])],-1))])):z("",!0),e("div",Lm,[D(h(se),{type:"button",variant:"outline",onClick:$,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium px-6 py-2 rounded-lg transition-all duration-200"},{default:H(()=>i[49]||(i[49]=[W(" Back to Billing ")])),_:1,__:[49]}),D(h(se),{type:"submit",loading:u.value,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:H(()=>i[50]||(i[50]=[W(" Review Order ")])),_:1,__:[50]},8,["loading"])])],32)])):z("",!0),d.value==="review"?(c(),p("div",zm,[i[68]||(i[68]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Review Your Order"),e("p",{class:"text-sm text-gray-600"},"Please review all details before placing your order")])],-1)),e("div",Om,[e("div",Fm,[i[55]||(i[55]=e("h4",{class:"font-medium text-gray-900 mb-4"},"Order Summary",-1)),e("div",Vm,[e("div",qm,[e("span",Um,"Items ("+S(h(s).cartCount)+")",1),e("span",Hm,S(h(s).formatCurrency(h(s).cartTotal)),1)]),i[54]||(i[54]=e("div",{class:"flex justify-between"},[e("span",{class:"text-gray-600"},"Shipping"),e("span",{class:"font-medium"},"Free")],-1)),e("div",Wm,[i[52]||(i[52]=e("span",{class:"text-gray-600"},"Tax",-1)),e("span",Km,S(h(s).formatCurrency(0)),1)]),e("div",Ym,[i[53]||(i[53]=e("span",{class:"text-lg font-bold text-gray-900"},"Total",-1)),e("span",Qm,S(h(s).formatCurrency(h(s).cartTotal)),1)])])]),e("div",Xm,[e("div",Gm,[i[59]||(i[59]=e("h4",{class:"font-medium text-gray-900 mb-3"},"Customer Information",-1)),e("div",Jm,[e("p",null,[i[56]||(i[56]=e("span",{class:"text-gray-600"},"Name:",-1)),W(" "+S(_.value.firstName)+" "+S(_.value.lastName),1)]),e("p",null,[i[57]||(i[57]=e("span",{class:"text-gray-600"},"Email:",-1)),W(" "+S(_.value.email),1)]),e("p",null,[i[58]||(i[58]=e("span",{class:"text-gray-600"},"Phone:",-1)),W(" "+S(_.value.phone),1)])])]),e("div",Zm,[i[60]||(i[60]=e("h4",{class:"font-medium text-gray-900 mb-3"},"Billing Address",-1)),e("div",e1,[e("p",null,S(M.value.address),1),e("p",null,S(M.value.city)+", "+S(M.value.state)+" "+S(M.value.postalCode),1),e("p",null,S(M.value.country),1)])])]),e("div",t1,[i[61]||(i[61]=e("h4",{class:"font-medium text-gray-900 mb-3"},"Payment Method",-1)),e("div",s1,[e("p",null,S((K=F())==null?void 0:K.name),1),v.value.method==="dpopay"?(c(),p("p",r1," Secure payment via DPO Gateway ")):v.value.method==="card"?(c(),p("p",o1," **** **** **** "+S(v.value.cardNumber.slice(-4)),1)):v.value.method==="cash"?(c(),p("p",a1," Payment on delivery ")):v.value.method==="bank"?(c(),p("p",n1," Direct bank transfer ")):z("",!0)])]),b.value?(c(),p("div",i1,[e("div",l1,[i[63]||(i[63]=e("div",{class:"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"},null,-1)),e("div",d1,[i[62]||(i[62]=e("h4",{class:"text-sm font-medium text-blue-800 mb-1"},"Processing Your Order",-1)),e("p",u1,S(l.value||"Please wait while we process your order..."),1)])])])):w.value?(c(),p("div",c1,[e("div",m1,[i[65]||(i[65]=e("svg",{class:"w-5 h-5 text-red-600 mt-0.5 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)),e("div",f1,[i[64]||(i[64]=e("h4",{class:"text-sm font-medium text-red-800 mb-1"},"Order Processing Error",-1)),e("p",p1,S(w.value),1),e("button",{onClick:U,class:"mt-2 text-xs text-red-600 hover:text-red-800 underline"}," Dismiss ")])])])):z("",!0),e("div",v1,[he(e("input",{id:"terms","onUpdate:modelValue":i[17]||(i[17]=N=>I.value=N),type:"checkbox",class:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[Ye,I.value]]),i[66]||(i[66]=e("label",{for:"terms",class:"text-sm text-gray-700"},[W(" I agree to the "),e("a",{href:"#",class:"text-blue-600 hover:text-blue-500"},"Terms and Conditions"),W(" and "),e("a",{href:"#",class:"text-blue-600 hover:text-blue-500"},"Privacy Policy")],-1))]),e("div",g1,[D(h(se),{type:"button",variant:"outline",onClick:$,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium px-6 py-2 rounded-lg transition-all duration-200"},{default:H(()=>i[67]||(i[67]=[W(" Back to Payment ")])),_:1,__:[67]}),D(h(se),{onClick:V,loading:b.value,disabled:!I.value||b.value,class:J(["font-bold px-8 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200",w.value?"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white":"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white"])},{default:H(()=>[b.value?(c(),p("span",h1,"Processing Order...")):w.value?(c(),p("span",y1,"Try Again")):(c(),p("span",b1,"Place Order"))]),_:1},8,["loading","disabled","class"])])])])):z("",!0)])]),e("div",x1,[e("div",w1,[e("div",_1,[e("div",k1,[e("div",C1,[i[70]||(i[70]=e("div",{class:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e("div",null,[i[69]||(i[69]=e("h3",{class:"text-lg font-bold"},"Order Summary",-1)),e("p",$1,S(h(s).cartCount)+" "+S(h(s).cartCount===1?"item":"items")+" in cart",1)])])]),e("div",S1,[e("div",M1,[(c(!0),p(me,null,fe(h(s).items,N=>(c(),p("div",{key:N.id,class:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"},[i[71]||(i[71]=e("div",{class:"w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",A1,[e("p",j1,S(N.name),1),e("p",T1,"Qty: "+S(N.quantity),1)]),e("div",I1,S(h(s).formatCurrency(h(s).getItemSubtotal(N))),1)]))),128))]),e("div",B1,[e("div",E1,[i[72]||(i[72]=e("span",{class:"text-gray-600"},"Subtotal",-1)),e("span",N1,S(h(s).formatCurrency(h(s).cartTotal)),1)]),i[75]||(i[75]=e("div",{class:"flex justify-between text-sm"},[e("span",{class:"text-gray-600"},"Shipping"),e("span",{class:"font-medium text-green-600"},"Free")],-1)),e("div",D1,[i[73]||(i[73]=e("span",{class:"text-gray-600"},"Tax",-1)),e("span",P1,S(h(s).formatCurrency(0)),1)]),e("div",R1,[i[74]||(i[74]=e("span",{class:"text-gray-900"},"Total",-1)),e("span",L1,S(h(s).formatCurrency(h(s).cartTotal)),1)])])])]),i[76]||(i[76]=e("div",{class:"bg-white rounded-xl shadow-lg border border-gray-100 p-6"},[e("h4",{class:"font-bold text-gray-900 mb-4 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),W(" Secure Checkout ")]),e("div",{class:"space-y-3"},[e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" 256-bit SSL encryption ")]),e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" Secure payment processing ")]),e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" Money-back guarantee ")])])],-1))])])]))]}),_:1})]))}});const Lt=Ae(z1,[["__scopeId","data-v-a7a2dac4"]]),O1={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},F1={class:"relative bg-gradient-to-r from-green-800 via-green-700 to-emerald-600 overflow-hidden border-b border-green-600/30"},V1={key:0,class:"flex justify-center py-20"},q1={class:"text-center"},U1={key:1,class:"max-w-4xl mx-auto"},H1={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-8"},W1={class:"px-6 py-8 text-center"},K1={class:"inline-flex items-center space-x-2 bg-gray-50 rounded-lg px-4 py-2"},Y1={class:"text-sm font-bold text-gray-900 font-mono"},Q1={class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"},X1={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},G1={class:"p-6 space-y-4"},J1={class:"flex justify-between items-center py-2 border-b border-gray-100"},Z1={class:"text-sm font-mono text-gray-900"},ef={class:"flex justify-between items-center py-2 border-b border-gray-100"},tf={class:"text-lg font-bold text-gray-900"},sf={class:"flex justify-between items-center py-2 border-b border-gray-100"},rf={class:"text-sm text-gray-900"},of={key:0,class:"flex justify-between items-center py-2"},af={class:"text-sm font-mono text-gray-900"},nf={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},lf={class:"p-6"},df={class:"flex flex-col sm:flex-row gap-4 justify-center"},uf=pe({__name:"PaymentSuccess",setup(t){const s=Le(),r=Be(),o=P(!0),a=P(""),n=P(""),u=P(0);P("MWK");const b=P(new Date),w=P(""),l=f=>!f||f<=0?"MK 0":`MK ${f.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,d=f=>new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(f),I=()=>{window.location.href="/"},B=()=>{window.print()},T=async()=>{var f;try{(await fetch("/api/cart/clear",{method:"POST",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((f=document.querySelector('meta[name="csrf-token"]'))==null?void 0:f.getAttribute("content"))||""}})).ok&&console.log("Backend cart cleared successfully"),s.reset(),localStorage.removeItem("vertigo_cart"),localStorage.removeItem("cart_items"),localStorage.removeItem("vertigo_order_ref"),localStorage.removeItem("vertigo_transaction_token"),window.dispatchEvent(new CustomEvent("cart-cleared",{detail:{reason:"payment_success"}})),console.log("Cart cleared successfully after payment")}catch(_){console.error("Error clearing cart after payment:",_)}},y=()=>{try{const f=new URLSearchParams(window.location.search);a.value=f.get("CompanyRef")||localStorage.getItem("vertigo_order_ref")||"N/A",n.value=f.get("TransID")||"N/A",w.value=f.get("CCDapproval")||"";const _=localStorage.getItem("vertigo_payment_amount");u.value=_?parseFloat(_):0,b.value=new Date}catch(f){console.error("Error loading payment data:",f)}};return Te(async()=>{y(),await T(),r.success("Payment completed successfully!"),setTimeout(()=>{o.value=!1},1e3)}),(f,_)=>(c(),p("div",O1,[e("div",F1,[_[1]||(_[1]=ze('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5" data-v-828e1a7e></div><div class="absolute inset-0" data-v-828e1a7e><div class="absolute top-2 left-4 w-12 h-12 bg-green-400/10 rounded-full blur-md" data-v-828e1a7e></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-emerald-400/15 rounded-full blur-lg" data-v-828e1a7e></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl" data-v-828e1a7e></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;" data-v-828e1a7e></div></div>',2)),D(h($e),{class:"relative z-10"},{default:H(()=>_[0]||(_[0]=[e("div",{class:"py-6"},[e("div",{class:"flex items-center justify-between"},[e("div",{class:"flex items-center space-x-4"},[e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-500/30 to-emerald-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-green-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])]),e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Payment Successful "),e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-green-200"},[e("li",null,[e("a",{href:"/",class:"hover:text-green-100 transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-green-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",null,[e("a",{href:"/cart",class:"hover:text-green-100 transition-colors duration-200"},"Cart")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-green-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",null,[e("a",{href:"/checkout",class:"hover:text-green-100 transition-colors duration-200"},"Checkout")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-green-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Success")])])])]),e("div",{class:"hidden md:flex items-center space-x-3"},[e("div",{class:"flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20"},[e("div",{class:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),e("span",{class:"text-sm font-medium text-green-100"},"Payment Confirmed")])])])],-1)])),_:1,__:[0]})]),D(h($e),{class:"py-12"},{default:H(()=>[o.value?(c(),p("div",V1,[e("div",q1,[D(h(Ue),{size:"lg"}),_[2]||(_[2]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading payment details...",-1))])])):(c(),p("div",U1,[e("div",H1,[e("div",W1,[_[4]||(_[4]=e("div",{class:"flex justify-center mb-6"},[e("div",{class:"w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg success-checkmark"},[e("svg",{class:"w-10 h-10 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])])],-1)),_[5]||(_[5]=e("h2",{class:"text-3xl font-bold text-gray-900 mb-3"},"Payment Successful!",-1)),_[6]||(_[6]=e("p",{class:"text-lg text-gray-600 mb-6"}," Your payment has been processed successfully and your order is confirmed. ",-1)),e("div",K1,[_[3]||(_[3]=e("span",{class:"text-sm font-medium text-gray-600"},"Order Reference:",-1)),e("span",Y1,S(a.value),1)])])]),e("div",Q1,[e("div",X1,[_[12]||(_[12]=e("div",{class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},[e("h3",{class:"text-lg font-bold text-gray-900 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})]),W(" Payment Information ")])],-1)),e("div",G1,[e("div",J1,[_[7]||(_[7]=e("span",{class:"text-sm font-medium text-gray-600"},"Transaction ID:",-1)),e("span",Z1,S(n.value),1)]),e("div",ef,[_[8]||(_[8]=e("span",{class:"text-sm font-medium text-gray-600"},"Amount:",-1)),e("span",tf,S(l(u.value)),1)]),_[11]||(_[11]=e("div",{class:"flex justify-between items-center py-2 border-b border-gray-100"},[e("span",{class:"text-sm font-medium text-gray-600"},"Payment Method:"),e("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"}," DPO Pay ")],-1)),e("div",sf,[_[9]||(_[9]=e("span",{class:"text-sm font-medium text-gray-600"},"Payment Date:",-1)),e("span",rf,S(d(b.value)),1)]),w.value?(c(),p("div",of,[_[10]||(_[10]=e("span",{class:"text-sm font-medium text-gray-600"},"Approval Code:",-1)),e("span",af,S(w.value),1)])):z("",!0)])]),_[13]||(_[13]=e("div",{class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},[e("div",{class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},[e("h3",{class:"text-lg font-bold text-gray-900 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})]),W(" Order Status ")])]),e("div",{class:"p-6"},[e("div",{class:"text-center"},[e("div",{class:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 mb-4"},[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" Order Confirmed ")]),e("p",{class:"text-gray-600 mb-6"}," Your order has been confirmed and is being processed. You will receive an email confirmation shortly. "),e("div",{class:"bg-blue-50 rounded-lg p-4"},[e("h4",{class:"text-sm font-semibold text-blue-900 mb-2"},"What's Next?"),e("ul",{class:"text-sm text-blue-800 space-y-1"},[e("li",null,"• Email confirmation sent"),e("li",null,"• Items marked as sold"),e("li",null,"• Cart automatically cleared")])])])])],-1))]),e("div",nf,[e("div",lf,[e("div",df,[D(h(se),{variant:"primary",size:"lg",onClick:I,class:"flex items-center justify-center"},{default:H(()=>_[14]||(_[14]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Continue Shopping ")])),_:1,__:[14]}),D(h(se),{variant:"outline",size:"lg",onClick:B,class:"flex items-center justify-center"},{default:H(()=>_[15]||(_[15]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"})],-1),W(" Print Receipt ")])),_:1,__:[15]})])])])]))]),_:1})]))}});const zt=Ae(uf,[["__scopeId","data-v-828e1a7e"]]),cf={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},mf={key:0,class:"flex justify-center items-center min-h-screen"},ff={class:"text-center"},pf={key:1,class:"flex justify-center items-center min-h-screen"},vf={class:"text-center"},gf={class:"text-gray-600 mb-4"},hf={key:2,class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},yf={class:"py-6"},bf={class:"flex items-center justify-between"},xf={class:"flex items-center space-x-4"},wf={class:"mt-1"},_f={class:"flex items-center space-x-2 text-sm text-slate-300"},kf={class:"text-gray-100 font-medium truncate max-w-xs"},Cf={class:"flex items-center space-x-3"},$f={key:0,class:"max-w-7xl mx-auto"},Sf={class:"grid grid-cols-1 xl:grid-cols-3 gap-8 mb-8"},Mf={class:"xl:col-span-2 space-y-6"},Af={class:"relative bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden group"},jf={class:"aspect-[4/3] bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center relative"},Tf=["src","alt"],If={key:1,class:"text-gray-400 text-center"},Bf={class:"absolute top-4 left-4 right-4 flex justify-between items-start"},Ef={class:"bg-white/90 backdrop-blur-sm rounded-xl px-3 py-2 shadow-lg"},Nf={class:"text-xs font-bold"},Df={class:"bg-white/90 backdrop-blur-sm rounded-xl px-3 py-2 shadow-lg"},Pf={class:"text-xs font-medium text-gray-600"},Rf={key:0,class:"grid grid-cols-5 gap-3"},Lf=["onClick"],zf=["src","alt"],Of={class:"xl:col-span-1"},Ff={class:"sticky top-8 space-y-6"},Vf={class:"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden"},qf={class:"p-8"},Uf={class:"text-3xl font-bold text-gray-900 mb-6 leading-tight"},Hf={class:"mb-8"},Wf={class:"text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-100"},Kf={class:"text-sm font-medium text-gray-600 mb-2"},Yf={class:"text-4xl font-bold text-gray-900 mb-2"},Qf={key:0,class:"text-sm text-gray-500"},Xf={class:"space-y-4 mb-8"},Gf={key:0,class:"flex items-center justify-between py-3 border-b border-gray-100"},Jf={class:"font-mono text-gray-900 font-semibold"},Zf={key:1,class:"flex items-center justify-between py-3 border-b border-gray-100"},ep={class:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold"},tp={key:2,class:"flex items-center justify-between py-3"},sp={class:"text-sm font-bold"},rp={class:"space-y-4"},op={key:0},ap={key:0,class:"bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-4"},np={class:"flex items-start space-x-3"},ip={class:"text-sm text-yellow-700 mt-1"},lp={class:"mt-3"},dp={class:"relative"},up={key:0,class:"mt-2 text-sm text-red-600 bg-red-50 p-3 rounded-xl"},cp={key:1,class:"mt-2 text-sm text-green-600 bg-green-50 p-3 rounded-xl"},mp={class:"mt-2 text-xs text-gray-500 bg-gray-50 p-3 rounded-xl"},fp={key:1},pp={class:"space-y-4"},vp={key:1,class:"space-y-3"},gp={class:"grid grid-cols-2 gap-3"},hp={class:"pt-6 border-t border-gray-200"},yp={key:0,class:"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden"},bp={class:"p-8"},xp={class:"prose prose-lg max-w-none"},wp={class:"text-gray-700 leading-relaxed text-lg"},_p={class:"max-w-6xl max-h-full p-6 relative"},kp={class:"absolute top-2 left-6 right-6 flex items-center justify-between z-10"},Cp={class:"bg-black bg-opacity-50 backdrop-blur-sm rounded-lg px-4 py-2"},$p={class:"text-white font-medium text-sm"},Sp={class:"bg-white rounded-xl shadow-2xl overflow-hidden"},Mp=["src","alt"],Ot=pe({__name:"ItemDetail",setup(t){const s=Le(),r=nt(),o=Be(),{isAuthenticated:a,user:n,initialize:u}=fr(),b=Ns(),w=qe(),l=P(null),d=P(!0),I=P(null),B=P(0),T=P(!1),y=P(null),f=P(!1),_=P(!1),R=P(!1);P(!1);const M=P(""),g=P(!1),v=P(null),x=P(!1),j=q(()=>{var k,X,m;if(!((k=l.value)!=null&&k.media)||l.value.media.length===0)return((X=l.value)==null?void 0:X.image)||((m=l.value)==null?void 0:m.cropped)||null;const C=l.value.media[B.value];return(C==null?void 0:C.original_url)||(C==null?void 0:C.url)||null}),A=q(()=>l.value?s.isInCart(l.value.id):!1);q(()=>l.value?r.isInWatchlist(l.value.id):!1);const O=q(()=>{var C,k;return l.value?((C=l.value.auction_type)==null?void 0:C.type)!=="live"?!0:((k=v.value)==null?void 0:k.subscribed)===!0:!1}),Z=q(()=>{var C,k,X;return!l.value||((C=l.value.auction_type)==null?void 0:C.type)!=="live"?null:x.value?"Checking subscription status...":((k=v.value)==null?void 0:k.subscribed)===!1?`You need to subscribe to "${v.value.auction_type||((X=l.value.auction_type)==null?void 0:X.name)||"this live auction"}" to place bids on this live auction item.`:null}),oe=async()=>{var C,k,X,m;try{d.value=!0,I.value=null;const i=b.params.id;if(!i)throw new Error("Invalid item ID");const K=await fetch(`/ajax-item/${i}`);if(!K.ok)throw K.status===404?new Error("Item not found"):K.status>=500?new Error("Server error. Please try again later."):new Error("Failed to load item");const N=await K.json();if(!N||!N.id)throw new Error("Invalid item data received");if(l.value=N,((C=N.auction_type)==null?void 0:C.type)!=="cash"){const G=N.bid_amount||0,ie=N.target_amount||0,le=Math.max(G,ie)+1;y.value=le.toString();const re=U(le.toString());setTimeout(()=>{const we=document.getElementById("bidAmount");we&&(we.value=re)},100)}((k=N.auction_type)==null?void 0:k.type)==="live"&&a.value&&N.auction_type_id&&(console.log("Item loaded, checking subscription for live auction:",{auction_type_id:N.auction_type_id,auction_type_name:(X=N.auction_type)==null?void 0:X.name,auction_type_type:(m=N.auction_type)==null?void 0:m.type}),await de(N.auction_type_id))}catch(i){console.error("Error loading item:",i),I.value=i instanceof Error?i.message:"Failed to load item"}finally{d.value=!1}},de=async C=>{var k;if(!a.value){v.value={subscribed:!1,message:"User not authenticated"};return}try{x.value=!0,console.log("Checking subscription for auction type ID:",C);const X=await fetch(`/api/check-subscription/${C}`,{headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((k=document.querySelector('meta[name="csrf-token"]'))==null?void 0:k.getAttribute("content"))||""},credentials:"include"});if(X.ok){const m=await X.json();console.log("Subscription check response:",m),v.value=m}else console.error("Failed to check subscription status:",X.status),v.value={subscribed:!1,message:"Failed to check subscription"}}catch(X){console.error("Error checking subscription status:",X),v.value={subscribed:!1,message:"Error checking subscription"}}finally{x.value=!1}},te=C=>{B.value=C},V=()=>{T.value=!0},Y=()=>{T.value=!1},ne=C=>new Intl.NumberFormat("en-MW",{style:"currency",currency:"MWK",minimumFractionDigits:0,maximumFractionDigits:0}).format(C),L=C=>{var k;switch((k=C.auction_type)==null?void 0:k.type){case"cash":return C.target_amount||0;case"online":case"live":return C.bid_amount||C.target_amount||0;default:return C.target_amount||0}},$=C=>{if(!C.date_to)return null;const k=new Date(C.date_to),X=new Date;if(k<X)return"Ended";const m=k.getTime()-X.getTime();return Math.floor(m/(1e3*60*60))<24?"Ending Soon":"Active"},F=C=>{const k=$(C),X="px-2 py-1 rounded-full text-xs font-medium";switch(k){case"Ended":return`${X} bg-red-100 text-red-800`;case"Ending Soon":return`${X} bg-yellow-100 text-yellow-800`;case"Active":return`${X} bg-green-100 text-green-800`;default:return`${X} bg-gray-100 text-gray-800`}},U=C=>C.replace(/[^0-9]/g,"").replace(/\B(?=(\d{3})+(?!\d))/g,","),Q=C=>{const k=C.target;if(k.value&&!k.value.includes(",")){const X=U(k.value);k.value=X}},ee=C=>{var le,re;const k=C.target,m=k.value.replace(/[^0-9]/g,""),i=U(m);if(k.value=i,y.value=m,!m){M.value="",g.value=!1;return}const K=parseInt(m),N=((le=l.value)==null?void 0:le.bid_amount)||0,G=((re=l.value)==null?void 0:re.target_amount)||0,ie=Math.max(N,G)+1;if(isNaN(K)||K<=0){M.value="Please enter a valid amount",g.value=!1;return}if(K<ie){M.value=`Minimum bid is ${ne(ie)}`,g.value=!1;return}M.value="",g.value=!0},ue=async()=>{var C;if(!(!l.value||!y.value)){if(!a.value){o.error("You must be signed in to place a bid","Authentication Required");return}try{f.value=!0;const k=typeof y.value=="string"?y.value.replace(/,/g,""):y.value,X=parseInt(k.toString())||0;if(isNaN(X)||X<=0){o.error("Please enter a valid bid amount","Invalid Bid");return}console.log("Placing bid:",{item_id:l.value.id,amount:X});const m=await fetch("/ajax-bid",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":((C=document.querySelector('meta[name="csrf-token"]'))==null?void 0:C.getAttribute("content"))||""},body:JSON.stringify({item_id:l.value.id,amount:X})});if(console.log("Response status:",m.status),!m.ok){const K=await m.text();throw console.error("Bid error response:",K),new Error(`Failed to place bid: ${m.status} ${K}`)}const i=await m.json();console.log("Bid response data:",i),o.success(`Your bid of ${ne(X)} has been placed successfully!`,"Bid Placed"),await oe()}catch(k){o.error(k instanceof Error?k.message:"Failed to place bid","Bid Failed")}finally{f.value=!1}}},xe=async()=>{if(l.value)try{_.value=!0,await s.addToCart(l.value)&&o.success(`${l.value.name} has been added to your cart.`,"Added to Cart")}catch(C){o.error(C instanceof Error?C.message:"Failed to add item to cart","Cart Error")}finally{_.value=!1}},Re=async()=>{if(l.value)try{R.value=!0,await s.removeFromCart(l.value)&&o.info(`${l.value.name} has been removed from your cart.`,"Removed from Cart")}catch(C){o.error(C instanceof Error?C.message:"Failed to remove item from cart","Cart Error")}finally{R.value=!1}},Ne=()=>{w.push("/checkout")},je=()=>{w.back()},Oe=async C=>{var k,X;o.success("You are now signed in and can place bids!","Welcome!"),((X=(k=l.value)==null?void 0:k.auction_type)==null?void 0:X.type)==="live"&&l.value.auction_type_id&&await de(l.value.auction_type_id),oe()};return Te(async()=>{await u();const C=window.cartItems||[];C.length>0?s.initializeCart(C):await s.fetchCart(),oe()}),(C,k)=>{var m,i;const X=Zt("router-link");return c(),p("div",cf,[d.value?(c(),p("div",mf,[e("div",ff,[D(h(Ue),{size:"lg"}),k[2]||(k[2]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading item details...",-1))])])):I.value?(c(),p("div",pf,[e("div",vf,[k[4]||(k[4]=e("div",{class:"text-red-500 mb-4"},[e("svg",{class:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),k[5]||(k[5]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-2"},"Item Not Found",-1)),e("p",gf,S(I.value),1),D(h(se),{onClick:je,variant:"outline"},{default:H(()=>k[3]||(k[3]=[W(" Go Back ")])),_:1,__:[3]})])])):l.value?(c(),p("div",hf,[k[13]||(k[13]=ze('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5"></div><div class="absolute inset-0"><div class="absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md"></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg"></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;"></div></div>',2)),D(h($e),{class:"relative z-10"},{default:H(()=>[e("div",yf,[e("div",bf,[e("div",xf,[k[11]||(k[11]=e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500/30 to-indigo-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-blue-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[k[10]||(k[10]=e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Item Details ",-1)),e("nav",wf,[e("ol",_f,[e("li",null,[D(X,{to:"/",class:"hover:text-blue-200 transition-colors duration-200"},{default:H(()=>k[6]||(k[6]=[W("Home")])),_:1,__:[6]})]),k[8]||(k[8]=e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])],-1)),e("li",null,[D(X,{to:"/auctions",class:"hover:text-blue-200 transition-colors duration-200"},{default:H(()=>k[7]||(k[7]=[W("Auctions")])),_:1,__:[7]})]),k[9]||(k[9]=e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])],-1)),e("li",kf,S(l.value.name),1)])])])]),e("div",Cf,[D(h(se),{variant:"outline",size:"sm",onClick:je,class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:H(()=>k[12]||(k[12]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Back ")])),_:1,__:[12]})])])])]),_:1})])):z("",!0),D(h($e),{class:"py-8"},{default:H(()=>{var K,N,G,ie;return[l.value?(c(),p("div",$f,[e("div",Sf,[e("div",Mf,[e("div",Af,[e("div",jf,[j.value?(c(),p("img",{key:0,src:j.value,alt:l.value.name,class:"w-full h-full object-cover cursor-pointer group-hover:scale-105 transition-transform duration-500",onClick:V},null,8,Tf)):(c(),p("div",If,k[14]||(k[14]=[e("svg",{class:"w-20 h-20 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-lg font-medium"},"No image available",-1)]))),e("div",Bf,[e("div",Ef,[$(l.value)?(c(),p("div",{key:0,class:J(F(l.value))},[e("span",Nf,S($(l.value)),1)],2)):z("",!0)]),e("div",Df,[e("span",Pf,S(((K=l.value.auction_type)==null?void 0:K.name)||"Auction Item"),1)])]),k[15]||(k[15]=e("div",{class:"absolute bottom-4 right-4 bg-black/50 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"})])],-1))])]),l.value.media&&l.value.media.length>1?(c(),p("div",Rf,[(c(!0),p(me,null,fe(l.value.media,(le,re)=>(c(),p("div",{key:re,class:J(["aspect-square bg-gray-100 rounded-xl overflow-hidden cursor-pointer border-2 transition-all duration-300 hover:shadow-lg",B.value===re?"border-blue-500 ring-2 ring-blue-200 shadow-lg":"border-gray-200 hover:border-gray-300"]),onClick:we=>te(re)},[e("img",{src:le.url||le.original_url,alt:`${l.value.name} - Image ${re+1}`,class:"w-full h-full object-cover hover:scale-110 transition-transform duration-300"},null,8,zf)],10,Lf))),128))])):z("",!0)]),e("div",Of,[e("div",Ff,[e("div",Vf,[e("div",qf,[e("h1",Uf,S(l.value.name),1),e("div",Hf,[e("div",Wf,[e("p",Kf,S(((N=l.value.auction_type)==null?void 0:N.type)==="cash"?"Price":"Current Bid"),1),e("div",Yf,S(ne(L(l.value))),1),((G=l.value.auction_type)==null?void 0:G.type)!=="cash"?(c(),p("p",Qf," Starting: "+S(ne(l.value.target_amount||0)),1)):z("",!0)])]),e("div",Xf,[l.value.code||l.value.reference_number?(c(),p("div",Gf,[k[16]||(k[16]=e("span",{class:"text-gray-600 font-medium"},"Reference",-1)),e("span",Jf,S(l.value.code||l.value.reference_number),1)])):z("",!0),l.value.auction_type?(c(),p("div",Zf,[k[17]||(k[17]=e("span",{class:"text-gray-600 font-medium"},"Category",-1)),e("span",ep,S(l.value.auction_type.name),1)])):z("",!0),$(l.value)?(c(),p("div",tp,[k[18]||(k[18]=e("span",{class:"text-gray-600 font-medium"},"Status",-1)),e("div",{class:J(F(l.value))},[e("span",sp,S($(l.value)),1)],2)])):z("",!0)]),e("div",rp,[((ie=l.value.auction_type)==null?void 0:ie.type)!=="cash"?(c(),p("div",op,[D(h(is),{title:"Sign in to place a bid",message:"You need to be signed in to participate in bidding on this item.","fallback-style":"card",onAuthSuccess:Oe},{default:H(()=>{var le;return[((le=l.value.auction_type)==null?void 0:le.type)==="live"&&!O.value?(c(),p("div",ap,[e("div",np,[k[21]||(k[21]=e("svg",{class:"w-5 h-5 text-yellow-600 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("div",null,[k[20]||(k[20]=e("h4",{class:"text-sm font-medium text-yellow-800"},"Subscription Required",-1)),e("p",ip,S(Z.value),1),e("div",lp,[D(X,{to:"/register-bid",class:"inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"},{default:H(()=>k[19]||(k[19]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),W(" Subscribe to Live Auction ")])),_:1,__:[19]})])])])])):z("",!0),O.value?(c(),p("form",{key:1,onSubmit:Ce(ue,["prevent"]),class:"space-y-4"},[e("div",null,[k[24]||(k[24]=e("label",{for:"bidAmount",class:"block text-sm font-bold text-gray-700 mb-3"}," Place Your Bid ",-1)),e("div",dp,[k[22]||(k[22]=e("span",{class:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-bold"},"MWK",-1)),he(e("input",{id:"bidAmount","onUpdate:modelValue":k[0]||(k[0]=re=>y.value=re),type:"text",inputmode:"numeric",pattern:"[0-9]*",class:"w-full pl-16 pr-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg font-semibold transition-all duration-200",placeholder:"Enter your bid",required:"",onInput:ee,onFocus:Q},null,544),[[Pe,y.value]])]),M.value?(c(),p("div",up,S(M.value),1)):y.value&&g.value?(c(),p("div",cp," ✓ Valid bid amount: "+S(ne(parseInt(y.value.toString().replace(/,/g,"")))),1)):z("",!0),e("p",mp,[k[23]||(k[23]=e("svg",{class:"w-3 h-3 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),W(" Minimum bid: "+S(ne(Math.max(l.value.bid_amount||0,l.value.target_amount||0)+1)),1)])]),D(h(se),{type:"submit",loading:f.value,disabled:!y.value||!g.value,class:"w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-bold py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",size:"lg"},{default:H(()=>k[25]||(k[25]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})],-1),W(" Place Bid ")])),_:1,__:[25]},8,["loading","disabled"])],32)):z("",!0)]}),_:1})])):(c(),p("div",fp,[e("div",pp,[A.value?(c(),p("div",vp,[k[29]||(k[29]=e("div",{class:"p-4 bg-green-50 border-2 border-green-200 rounded-2xl"},[e("div",{class:"flex items-center justify-center text-green-800"},[e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),e("span",{class:"font-bold"},"Item is in your cart")])],-1)),e("div",gp,[D(h(se),{onClick:Re,loading:R.value,variant:"outline",class:"border-2 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 font-bold py-3 rounded-xl",size:"lg"},{default:H(()=>k[27]||(k[27]=[W(" Remove ")])),_:1,__:[27]},8,["loading"]),D(h(se),{onClick:Ne,class:"bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-bold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300",size:"lg"},{default:H(()=>k[28]||(k[28]=[W(" Checkout ")])),_:1,__:[28]})])])):(c(),ce(h(se),{key:0,onClick:xe,loading:_.value,class:"w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-bold py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",size:"lg"},{default:H(()=>k[26]||(k[26]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8m-8 0a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z"})],-1),W(" Add to Cart ")])),_:1,__:[26]},8,["loading"]))])]))]),e("div",hp,[D(h(se),{onClick:je,variant:"outline",class:"w-full border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-bold py-3 rounded-xl",size:"lg"},{default:H(()=>k[30]||(k[30]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Back to Listings ")])),_:1,__:[30]})])])])])])]),l.value.description?(c(),p("div",yp,[e("div",bp,[k[31]||(k[31]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("div",null,[e("h2",{class:"text-2xl font-bold text-gray-900"},"Description"),e("p",{class:"text-gray-600"},"Detailed information about this item")])],-1)),e("div",xp,[e("p",wp,S(l.value.description),1)])])])):z("",!0)])):z("",!0)]}),_:1}),T.value?(c(),p("div",{key:3,class:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 backdrop-blur-sm",onClick:Y},[e("div",_p,[e("div",kp,[e("div",Cp,[e("h3",$p,S((m=l.value)==null?void 0:m.name),1)]),e("button",{onClick:Y,class:"bg-black bg-opacity-50 backdrop-blur-sm rounded-full p-2 text-white hover:bg-opacity-70 transition-all duration-200"},k[32]||(k[32]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Sp,[j.value?(c(),p("img",{key:0,src:j.value,alt:(i=l.value)==null?void 0:i.name,class:"max-w-full max-h-[80vh] object-contain mx-auto block",onClick:k[1]||(k[1]=Ce(()=>{},["stop"]))},null,8,Mp)):z("",!0)]),k[33]||(k[33]=e("div",{class:"absolute bottom-2 left-1/2 transform -translate-x-1/2"},[e("div",{class:"bg-black bg-opacity-50 backdrop-blur-sm rounded-lg px-4 py-2"},[e("p",{class:"text-white text-xs"},"Click outside to close")])],-1))])])):z("",!0)])}}}),Ap={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},jp={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Tp={class:"grid grid-cols-1 lg:grid-cols-5 gap-8"},Ip={class:"lg:col-span-2 lg:order-1"},Bp={class:"space-y-6"},Ep={class:"lg:col-span-3 lg:order-2"},Np={class:"p-6"},Dp={key:0,class:"mt-1 text-sm text-red-600"},Pp={key:1,class:"mt-1 text-sm text-gray-500"},Rp={class:"bg-gray-50 rounded-lg p-4"},Lp={class:"flex items-start"},zp={class:"flex items-center h-5"},Op=["disabled"],Fp={class:"flex justify-end pt-4"},Vp=pe({__name:"RegisterBid",setup(t){qe();const s=Be(),r=ge(),o=P(!1),a=P(!1),n=P(""),u=P(""),b=P([]),w=P({referenceNumber:"",selectedAuction:null,agreedToTerms:!1}),l=P({referenceNumber:"",auctionTypeId:"",agreedToTerms:""}),d=q(()=>b.value.map(R=>({key:R.id.toString(),label:R.name,description:R.description||`${R.type} auction`,value:R.id}))),I=q(()=>w.value.referenceNumber.trim()!==""&&w.value.selectedAuction!==null&&w.value.agreedToTerms&&!o.value),B=()=>{l.value={referenceNumber:"",auctionTypeId:"",agreedToTerms:""},n.value=""},T=()=>{B();let R=!0;return w.value.referenceNumber.trim()||(l.value.referenceNumber="Bank deposit reference number is required",R=!1),w.value.selectedAuction||(l.value.auctionTypeId="Please select an auction to register for",R=!1),w.value.agreedToTerms||(l.value.agreedToTerms="required",R=!1),R},y=async()=>{var R,M;try{a.value=!0;const g={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},v=(R=document.querySelector('meta[name="csrf-token"]'))==null?void 0:R.getAttribute("content");v&&(g["X-CSRF-TOKEN"]=v);const x=await ke.get("/api/live-auctions-session",{headers:g,withCredentials:!0});b.value=x.data.data||x.data}catch(g){console.error("Error fetching auction types:",g),((M=g.response)==null?void 0:M.status)===401?console.log("User not authenticated for auction types"):s.error("Failed to load available auctions")}finally{a.value=!1}},f=async()=>{var R,M,g;if(T())try{o.value=!0,B();const v=(R=document.querySelector('meta[name="csrf-token"]'))==null?void 0:R.getAttribute("content");if(!v){n.value="Security token not found. Please refresh the page.",o.value=!1;return}const x=new FormData;x.append("_token",v),x.append("reference_number",w.value.referenceNumber.trim()),x.append("auction_type_id",((M=w.value.selectedAuction)==null?void 0:M.value.toString())||"");const j=await fetch("/register-bid",{method:"POST",body:x,credentials:"same-origin",headers:{"X-Requested-With":"XMLHttpRequest",Accept:"application/json"}}),A=await j.json();if(j.ok&&A.success)u.value=A.message||`You have successfully registered to the ${((g=w.value.selectedAuction)==null?void 0:g.label)||"auction"}`,s.success(u.value),setTimeout(()=>{window.location.href="/shop"},1500);else{let O=A.error||A.message||"Registration failed. Please try again.";n.value=O,s.error(O)}}catch(v){console.error("Registration error:",v),n.value="Registration failed. Please check your connection and try again.",s.error(n.value)}finally{o.value=!1}},_=async R=>{console.log("Auth success in RegisterBid:",R),window.user=R,r.setUser(R),await y(),n.value=""};return Me(()=>r.isAuthenticated||!!window.user,async R=>{R&&b.value.length===0&&(console.log("User became authenticated, fetching auction types"),await y())},{immediate:!1}),Te(async()=>{var M;if(!document.querySelector('meta[name="csrf-token"]')){const g=document.createElement("meta");g.name="csrf-token",g.content=((M=window.Laravel)==null?void 0:M.csrfToken)||"",document.head.appendChild(g)}const R=window.user;R?(console.log("User authenticated via server data:",R),r.setUser(R),await y()):console.log("User not authenticated - AuthGuard will handle login")}),(R,M)=>(c(),p("div",Ap,[D(h(is),{title:"Sign in to register for auctions",message:"You need to be signed in to register for auction participation.","require-auth":!0,onAuthSuccess:_},{default:H(()=>[M[11]||(M[11]=e("div",{class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},[e("div",{class:"absolute inset-0 bg-gradient-to-b from-transparent to-black/5"}),e("div",{class:"absolute inset-0"},[e("div",{class:"absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md"}),e("div",{class:"absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg"}),e("div",{class:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl"})]),e("div",{class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},[e("div",{class:"text-center"},[e("h1",{class:"text-3xl font-bold text-white mb-2"},"Register for Auction"),e("p",{class:"text-slate-200 text-lg"},"Join live auctions and start bidding on amazing items")])])],-1)),e("div",jp,[e("div",Tp,[e("div",Ip,[e("div",Bp,[D(h(et),{class:"checkout-section order-summary sticky top-8"},{default:H(()=>M[5]||(M[5]=[e("div",{class:"p-8"},[e("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Registration Process"),e("div",{class:"space-y-6"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"1")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Make Bank Deposit"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Visit your bank or use online banking to make a deposit. Keep your deposit receipt as you'll need the reference number for registration. "),e("div",{class:"mt-2 text-xs text-blue-600 font-medium"}," 💡 Tip: Save a photo of your receipt ")])]),e("div",{class:"border-l-2 border-gray-200 ml-6 h-4"}),e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"2")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Select Your Auction"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Choose from available live auctions. Each auction has different items and schedules. Review the auction details before registering. "),e("div",{class:"mt-2 text-xs text-green-600 font-medium"}," 📅 Check auction dates and times ")])]),e("div",{class:"border-l-2 border-gray-200 ml-6 h-4"}),e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"3")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Complete Registration"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Fill out the registration form with your deposit reference number and agree to the terms and conditions. "),e("div",{class:"mt-2 text-xs text-purple-600 font-medium"}," ✅ Review terms carefully ")])]),e("div",{class:"border-l-2 border-gray-200 ml-6 h-4"}),e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"4")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Start Bidding"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Once registered, you can participate in the live auction. Place bids on items you're interested in and track your bidding activity. "),e("div",{class:"mt-2 text-xs text-orange-600 font-medium"}," 🎯 Good luck with your bids! ")])])])],-1)])),_:1,__:[5]}),D(h(et),{class:"checkout-section"},{default:H(()=>M[6]||(M[6]=[e("div",{class:"p-6"},[e("h4",{class:"text-lg font-semibold text-gray-900 mb-4"},"Important Information"),e("div",{class:"space-y-4"},[e("div",{class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-blue-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("h5",{class:"text-sm font-medium text-blue-900"},"Registration Requirements"),e("p",{class:"text-sm text-blue-700 mt-1"}," A valid bank deposit is required to participate in auctions. This ensures serious bidders and helps maintain auction integrity. ")])])]),e("div",{class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-yellow-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("h5",{class:"text-sm font-medium text-yellow-900"},"Auction Rules"),e("p",{class:"text-sm text-yellow-700 mt-1"}," Please familiarize yourself with our auction terms and bidding rules before participating. All sales are final. ")])])]),e("div",{class:"bg-green-50 border border-green-200 rounded-lg p-4"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-green-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("h5",{class:"text-sm font-medium text-green-900"},"Support Available"),e("p",{class:"text-sm text-green-700 mt-1"}," Need help? Our support team is available during auction hours to assist with registration and bidding questions. ")])])])])],-1)])),_:1,__:[6]}),D(h(et),{class:"checkout-section security-badge"},{default:H(()=>M[7]||(M[7]=[e("div",{class:"p-6"},[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-green-800"},"Secure & Protected"),e("p",{class:"text-sm text-green-700 mt-1"}," Your registration information is encrypted and securely stored. We use industry-standard security measures to protect your data. ")])])],-1)])),_:1,__:[7]})])]),e("div",Ep,[D(h(et),{class:"checkout-section form-section"},{default:H(()=>[e("div",Np,[M[10]||(M[10]=e("div",{class:"mb-6"},[e("h2",{class:"text-xl font-semibold text-gray-900 mb-2"},"Auction Registration"),e("p",{class:"text-gray-600"},"Complete your registration to participate in live auctions")],-1)),n.value?(c(),ce(h(Ft),{key:0,variant:"error",title:n.value,class:"mb-6",onClose:M[0]||(M[0]=g=>n.value="")},null,8,["title"])):z("",!0),u.value?(c(),ce(h(Ft),{key:1,variant:"success",title:u.value,class:"mb-6",onClose:M[1]||(M[1]=g=>u.value="")},null,8,["title"])):z("",!0),e("form",{onSubmit:Ce(f,["prevent"]),class:"space-y-6"},[e("div",null,[D(h(be),{modelValue:w.value.referenceNumber,"onUpdate:modelValue":M[2]||(M[2]=g=>w.value.referenceNumber=g),label:"Bank Deposit Reference Number",placeholder:"Enter your bank deposit reference number",required:"",error:l.value.referenceNumber,disabled:o.value,"help-text":"Enter the reference number from your bank deposit receipt"},null,8,["modelValue","error","disabled"])]),e("div",null,[M[8]||(M[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},[W(" Select Auction "),e("span",{class:"text-red-500"},"*")],-1)),D(h(Ps),{modelValue:w.value.selectedAuction,"onUpdate:modelValue":M[3]||(M[3]=g=>w.value.selectedAuction=g),items:d.value,placeholder:"Choose an auction to register for",disabled:o.value||a.value,error:l.value.auctionTypeId,searchable:""},null,8,["modelValue","items","disabled","error"]),l.value.auctionTypeId?(c(),p("p",Dp,S(l.value.auctionTypeId),1)):z("",!0),a.value?(c(),p("p",Pp," Loading available auctions... ")):z("",!0)]),e("div",Rp,[e("div",Lp,[e("div",zp,[he(e("input",{id:"terms","onUpdate:modelValue":M[4]||(M[4]=g=>w.value.agreedToTerms=g),type:"checkbox",class:J(["h-4 w-4 rounded transition-colors duration-200",l.value.agreedToTerms?"border-red-500 text-red-600 focus:ring-red-500 bg-red-50":"text-primary-600 focus:ring-primary-500 border-gray-300"]),disabled:o.value},null,10,Op),[[Ye,w.value.agreedToTerms]])]),M[9]||(M[9]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"terms",class:"font-medium text-gray-700"}," I agree to the terms and conditions "),e("p",{class:"text-gray-500"},[W(" By registering, you agree to our auction terms, bidding rules, and payment policies. "),e("a",{href:"#",class:"text-primary-600 hover:text-primary-500"},"Read full terms")])],-1))])]),e("div",Fp,[D(h(se),{type:"submit",variant:"primary",size:"lg",loading:o.value,disabled:!I.value,class:"btn-enhanced"},{default:H(()=>[o.value?(c(),p(me,{key:0},[W(" Registering... ")],64)):(c(),p(me,{key:1},[W(" Register for Auction ")],64))]),_:1},8,["loading","disabled"])])],32)])]),_:1})])])])]),_:1,__:[11]})]))}});const As=Ae(Vp,[["__scopeId","data-v-e8f87a63"]]),qp=at("bidDashboard",()=>{const t=P([]),s=P([]),r=P([]),o=P({total_bids:0,active_bids:0,won_auctions:0,lost_auctions:0,watchlist_count:0,total_bid_amount:0,average_bid_amount:0,highest_bid:0,win_rate:0}),a=P(!1),n=P(null),u=P(0),b=q(()=>t.value.length>0),w=q(()=>s.value.length>0),l=q(()=>r.value.length>0),d=async()=>{var x,j,A,O;const v=ge();if(!v.isAuthenticated)return f(),!1;a.value=!0,n.value=null;try{const Z={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},oe=(x=document.querySelector('meta[name="csrf-token"]'))==null?void 0:x.getAttribute("content");oe&&(Z["X-CSRF-TOKEN"]=oe),v.token&&(Z.Authorization=`Bearer ${v.token}`);const de=await fetch("/api/bid-dashboard",{headers:Z,credentials:"include"});if(!de.ok)throw new Error("Failed to fetch dashboard data");const te=await de.json();return t.value=Array.isArray(te.active_bids)?te.active_bids:((j=te.active_bids)==null?void 0:j.data)||[],s.value=Array.isArray(te.bid_history)?te.bid_history:((A=te.bid_history)==null?void 0:A.data)||[],r.value=Array.isArray(te.watchlist)?te.watchlist:((O=te.watchlist)==null?void 0:O.data)||[],o.value=te.stats||o.value,u.value=Date.now(),!0}catch(Z){return n.value=Z instanceof Error?Z.message:"Failed to fetch dashboard data",console.error("Dashboard fetch error:",Z),!1}finally{a.value=!1}},I=async(v=1,x=15,j="")=>{const A=ge();if(!A.isAuthenticated)return{data:[],meta:{}};try{const O=new URLSearchParams({page:v.toString(),per_page:x.toString(),...j&&{search:j}}),Z=await fetch(`/api/bid-dashboard/active-bids?${O}`,{headers:{Accept:"application/json",Authorization:`Bearer ${A.token}`}});if(!Z.ok)throw new Error("Failed to fetch active bids");const oe=await Z.json();return v===1?t.value=oe.data||[]:t.value.push(...oe.data||[]),oe}catch(O){return n.value=O instanceof Error?O.message:"Failed to fetch active bids",console.error("Active bids fetch error:",O),{data:[],meta:{}}}},B=async(v=1,x=15,j="",A="")=>{var Z;const O=ge();if(!O.isAuthenticated)return{data:[],meta:{}};try{const de=`/api/bid-dashboard/bid-history?${new URLSearchParams({page:v.toString(),per_page:x.toString(),...j&&{search:j},...A&&{status:A}})}`,te={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},V=(Z=document.querySelector('meta[name="csrf-token"]'))==null?void 0:Z.getAttribute("content");V&&(te["X-CSRF-TOKEN"]=V),O.token&&(te.Authorization=`Bearer ${O.token}`);const Y=await fetch(de,{headers:te,credentials:"include"});if(!Y.ok)throw new Error(`Failed to fetch bid history: ${Y.status} ${Y.statusText}`);const ne=await Y.json();return v===1?s.value=ne.data||[]:s.value.push(...ne.data||[]),ne}catch(oe){return n.value=oe instanceof Error?oe.message:"Failed to fetch bid history",console.error("Bid history fetch error:",oe),{data:[],meta:{}}}},T=async()=>{const v=ge();if(!v.isAuthenticated)return!1;try{const x=await fetch("/api/bid-dashboard/stats",{headers:{Accept:"application/json",Authorization:`Bearer ${v.token}`}});if(!x.ok)throw new Error("Failed to fetch stats");const j=await x.json();return o.value=j,!0}catch(x){return n.value=x instanceof Error?x.message:"Failed to fetch stats",console.error("Stats fetch error:",x),!1}},y=async()=>await d(),f=()=>{t.value=[],s.value=[],r.value=[],o.value={total_bids:0,active_bids:0,won_auctions:0,lost_auctions:0,watchlist_count:0,total_bid_amount:0,average_bid_amount:0,highest_bid:0,win_rate:0},n.value=null,u.value=0};return{activeBids:t,bidHistory:s,watchlistItems:r,stats:o,isLoading:a,error:n,lastSyncTime:u,hasActiveBids:b,hasBidHistory:w,hasWatchlistItems:l,fetchDashboardData:d,fetchActiveBids:I,fetchBidHistory:B,fetchStats:T,refreshDashboard:y,clearDashboard:f,getActiveBidById:v=>t.value.find(x=>x.id===v),getBidHistoryById:v=>s.value.find(x=>x.id===v),isActiveBid:v=>t.value.some(x=>x.id===v),initializeDashboard:async()=>{ge().isAuthenticated&&await d()}}}),Up={class:"bg-white -mx-4 -my-5 sm:-m-6"},Hp={class:"px-4 py-5 sm:px-6 border-b border-gray-200 bg-gray-50"},Wp={class:"flex items-center justify-between"},Kp={class:"flex items-center space-x-3"},Yp={class:"text-sm text-gray-600 mt-0.5 max-w-md truncate"},Qp={class:"p-6"},Xp={key:0,class:"flex flex-col items-center justify-center py-16"},Gp={key:1,class:"text-center py-16"},Jp={class:"text-gray-600 mb-6"},Zp={key:2},ev={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8"},tv={class:"bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200"},sv={class:"flex items-center"},rv={class:"ml-3"},ov={class:"text-lg font-bold text-blue-700"},av={class:"bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4 border border-green-200"},nv={class:"flex items-center"},iv={class:"ml-3"},lv={class:"text-lg font-bold text-green-700"},dv={class:"relative"},uv={class:"space-y-6"},cv={class:"relative flex-shrink-0"},mv={key:0,class:"absolute -top-1 -left-1 w-5 h-5 bg-green-100 rounded-full animate-pulse"},fv={class:"flex-1 min-w-0"},pv={class:"flex items-center justify-between"},vv={class:"flex items-center space-x-3"},gv={class:"w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center"},hv={class:"text-sm font-medium text-gray-600"},yv={class:"flex items-center space-x-2"},bv={class:"text-sm font-semibold text-gray-900"},xv={class:"text-xs text-gray-500 mt-0.5"},wv={class:"text-right"},_v={key:0,class:"flex items-center justify-end mt-1"},kv={class:"text-xs font-medium text-green-600"},Cv={key:3,class:"text-center py-16"},$v=pe({__name:"BidTimelineModal",props:{show:{type:Boolean},item:{}},emits:["close"],setup(t,{emit:s}){const r=t,o=P([]),a=P(!1),n=P(null),u=q(()=>new Set(o.value.map(d=>d.user_id)).size);q(()=>o.value.length>0?Math.max(...o.value.map(l=>l.bid_amount||0)):0),q(()=>o.value.length===0?0:o.value.reduce((d,I)=>d+(I.bid_amount||0),0)/o.value.length);const b=l=>{const d=new Date(l);return(new Date().getTime()-d.getTime())/(1e3*60)<=5},w=async()=>{var l,d;if((l=r.item)!=null&&l.id){a.value=!0,n.value=null;try{const I={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},B=(d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content");B&&(I["X-CSRF-TOKEN"]=B);const T=localStorage.getItem("auth_token");T&&(I.Authorization=`Bearer ${T}`);const y=await fetch(`/api/open-item/${r.item.id}/bids?limit=10`,{headers:I,credentials:"include"});if(!y.ok)throw new Error("Failed to fetch bid history");const f=await y.json();o.value=f.data||[]}catch(I){n.value=I instanceof Error?I.message:"Failed to load bid history",console.error("Error fetching bids:",I)}finally{a.value=!1}}};return Me(()=>r.show,l=>{l&&r.item&&w()}),(l,d)=>(c(),ce(h(Es),{show:l.show,onClose:d[1]||(d[1]=I=>l.$emit("close")),"max-width":"2xl",closeable:!0},{default:H(()=>{var I;return[e("div",Up,[e("div",Hp,[e("div",Wp,[e("div",Kp,[d[3]||(d[3]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),e("div",null,[d[2]||(d[2]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Bid History",-1)),e("p",Yp,S((I=l.item)==null?void 0:I.name),1)])]),e("button",{onClick:d[0]||(d[0]=B=>l.$emit("close")),class:"flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"},d[4]||(d[4]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("div",Qp,[a.value?(c(),p("div",Xp,d[5]||(d[5]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mb-4"},null,-1),e("p",{class:"text-sm text-gray-500"},"Loading bid history...",-1)]))):n.value?(c(),p("div",Gp,[d[7]||(d[7]=e("div",{class:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),d[8]||(d[8]=e("h4",{class:"text-lg font-medium text-gray-900 mb-2"},"Failed to load bid history",-1)),e("p",Jp,S(n.value),1),e("button",{onClick:w,class:"inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors duration-200"},d[6]||(d[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),W(" Try again ")]))])):o.value.length>0?(c(),p("div",Zp,[e("div",ev,[e("div",tv,[e("div",sv,[d[10]||(d[10]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"})])])],-1)),e("div",rv,[d[9]||(d[9]=e("p",{class:"text-sm font-medium text-blue-900"},"Total Bids",-1)),e("p",ov,S(o.value.length),1)])])]),e("div",av,[e("div",nv,[d[12]||(d[12]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])])],-1)),e("div",iv,[d[11]||(d[11]=e("p",{class:"text-sm font-medium text-green-900"},"Bidders",-1)),e("p",lv,S(u.value),1)])])])]),e("div",dv,[d[16]||(d[16]=e("div",{class:"absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"},null,-1)),e("div",uv,[(c(!0),p(me,null,fe(o.value,(B,T)=>{var y,f;return c(),p("div",{key:B.id,class:"relative flex items-start space-x-4 group"},[e("div",cv,[e("div",{class:J(["w-3 h-3 rounded-full border-2 border-white shadow-sm transition-all duration-200 group-hover:scale-110",T===0?"bg-green-500":"bg-gray-400"])},null,2),T===0?(c(),p("div",mv)):z("",!0)]),e("div",fv,[e("div",{class:J(["bg-white border border-gray-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-200 group-hover:border-gray-300",T===0?"ring-2 ring-green-100 border-green-200":""])},[e("div",pv,[e("div",vv,[e("div",gv,[e("span",hv,S((((y=B.user)==null?void 0:y.name)||"Anonymous").charAt(0).toUpperCase()),1)]),e("div",null,[e("div",yv,[e("span",bv,S(((f=B.user)==null?void 0:f.name)||"Anonymous Bidder"),1),T===0?(c(),ce(h(Qe),{key:0,variant:"success",size:"sm",class:"animate-pulse"},{default:H(()=>d[13]||(d[13]=[e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3l14 9-14 9V3z"})],-1),W(" Leading ")])),_:1,__:[13]})):z("",!0),b(B.created_at)?(c(),ce(h(Qe),{key:1,variant:"info",size:"sm"},{default:H(()=>d[14]||(d[14]=[W(" New ")])),_:1,__:[14]})):z("",!0)]),e("p",xv,S(h(qt)(B.created_at)),1)])]),e("div",wv,[e("div",{class:J(["text-lg font-bold transition-colors duration-200",T===0?"text-green-600":"text-gray-900"])},S(h(ps)(B.bid_amount)),3),T<o.value.length-1?(c(),p("div",_v,[d[15]||(d[15]=e("svg",{class:"w-3 h-3 text-green-500 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 11l5-5m0 0l5 5m-5-5v12"})],-1)),e("span",kv," +"+S(h(ps)(B.bid_amount-o.value[T+1].bid_amount)),1)])):z("",!0)])])],2)])])}),128))])])])):(c(),p("div",Cv,d[17]||(d[17]=[e("div",{class:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1),e("h4",{class:"text-lg font-medium text-gray-900 mb-2"},"No bids yet",-1),e("p",{class:"text-gray-500"},"Be the first to place a bid on this item!",-1)])))])])]}),_:1},8,["show"]))}}),Sv={key:0,class:"bg-white border border-gray-200 rounded-2xl overflow-hidden hover:shadow-lg hover:border-gray-300 transition-all duration-300 group"},Mv={class:"relative"},Av={class:"flex items-center justify-between"},jv={class:"flex items-center space-x-3"},Tv={class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Iv={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"},Bv={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"},Ev={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"},Nv={class:"text-right"},Dv={class:"text-xs text-white/80 font-medium uppercase tracking-wide"},Pv={class:"text-sm font-semibold text-white"},Rv={class:"p-6"},Lv={class:"flex items-start space-x-4"},zv={class:"flex-shrink-0 relative"},Ov={class:"relative overflow-hidden rounded-xl shadow-sm"},Fv=["src","alt"],Vv={key:0,class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center animate-pulse"},qv={class:"flex-1 min-w-0"},Uv={class:"flex items-start justify-between mb-3"},Hv={class:"flex-1"},Wv={class:"text-lg font-bold text-gray-900 truncate group-hover:text-gray-700 transition-colors duration-200"},Kv={class:"flex items-center mt-2 space-x-2"},Yv={class:"inline-flex items-center px-3 py-1 text-xs font-semibold bg-gray-100 text-gray-700 rounded-full"},Qv={key:0,class:"inline-flex items-center px-3 py-1 text-xs font-semibold bg-blue-50 text-blue-700 rounded-full"},Xv={class:"grid grid-cols-2 gap-4 mb-4"},Gv={class:"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200"},Jv={class:"text-xl font-bold text-blue-900"},Zv={class:"text-xs text-blue-600 mt-1"},eg={class:"flex items-center justify-between mb-2"},tg={key:0,class:"mb-4"},sg={class:"flex items-center justify-between mb-2"},rg={class:"w-full bg-gray-200 rounded-full h-2"},og={class:"mb-4"},ag={key:0,class:"flex items-center p-3 bg-green-50 border border-green-200 rounded-lg"},ng={key:1,class:"flex items-center p-3 bg-amber-50 border border-amber-200 rounded-lg"},ig={key:2,class:"flex items-center p-3 bg-gray-50 border border-gray-200 rounded-lg"},lg={class:"text-xs text-gray-600"},dg={class:"flex items-center justify-between"},ug={class:"flex items-center space-x-4"},cg=["disabled"],mg=["disabled"],fg={key:1,class:"bg-gray-50 border border-gray-200 rounded-xl p-5 opacity-60"},pg=pe({__name:"BidCard",props:{bid:{},variant:{}},emits:["view-details","increase-bid"],setup(t,{emit:s}){const r=t,o=s,a=P(!1),n=P(!1),u=q(()=>r.bid&&r.bid.item&&r.bid.item.id),b=q(()=>{var L;return((L=r.bid.item)==null?void 0:L.name)||"Unknown Item"}),w=q(()=>{var L;return((L=r.bid.auctionType)==null?void 0:L.name)||"Unknown Type"}),l=q(()=>{var L,$;return a.value?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+":((L=r.bid.item)==null?void 0:L.image)||(($=r.bid.item)==null?void 0:$.cropped)||"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+"}),d=q(()=>parseFloat(r.bid.bid_amount||"0")),I=q(()=>{var L;return parseFloat(((L=r.bid.item)==null?void 0:L.bid_amount)||"0")}),B=q(()=>{var L,$;return!r.bid.date_to&&!((L=r.bid.item)!=null&&L.date_to)?"No end date":Fs(r.bid.date_to||(($=r.bid.item)==null?void 0:$.date_to))}),T=q(()=>qt(r.bid.created_at)),y=q(()=>r.variant==="active"?R.value?"success":"warning":r.bid.closed_by===r.bid.user_id?"success":"secondary"),f=q(()=>r.variant==="active"?R.value?"Winning":"Outbid":r.bid.closed_by===r.bid.user_id?"Won":"Lost"),_=q(()=>{var F;if(r.variant!=="active"||!u.value)return!1;const L=new Date,$=r.bid.date_to?new Date(r.bid.date_to):(F=r.bid.item)!=null&&F.date_to?new Date(r.bid.item.date_to):null;return $&&$<=L?!1:d.value<I.value}),R=q(()=>r.variant!=="active"||!u.value?!1:d.value>=I.value&&d.value>0),M=q(()=>r.variant==="active"?R.value?"bg-gradient-to-r from-green-500 to-green-600":"bg-gradient-to-r from-amber-500 to-amber-600":r.bid.closed_by===r.bid.user_id?"bg-gradient-to-r from-green-500 to-green-600":"bg-gradient-to-r from-gray-500 to-gray-600"),g=q(()=>r.variant==="active"?R.value?"bg-green-600":"bg-amber-600":r.bid.closed_by===r.bid.user_id?"bg-green-600":"bg-gray-600"),v=q(()=>{var ue,xe;if(r.variant==="history")return qt(r.bid.date_to||((ue=r.bid.item)==null?void 0:ue.date_to)||r.bid.created_at);const L=r.bid.date_to||((xe=r.bid.item)==null?void 0:xe.date_to);if(!L)return"No end date";const $=new Date,U=new Date(L).getTime()-$.getTime();if(U<=0)return"Ended";const Q=Math.floor(U/(1e3*60*60)),ee=Math.floor(U%(1e3*60*60)/(1e3*60));return Q>24?`${Math.floor(Q/24)}d ${Q%24}h`:Q>0?`${Q}h ${ee}m`:`${ee}m`}),x=q(()=>{var Q;if(r.variant!=="active")return!1;const L=r.bid.date_to||((Q=r.bid.item)==null?void 0:Q.date_to);if(!L)return!1;const $=new Date;return new Date(L).getTime()-$.getTime()<=1e3*60*60&&!R.value}),j=q(()=>R.value?"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200":"bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 border border-red-200"),A=q(()=>R.value?"text-gray-700":"text-red-700"),O=q(()=>R.value?"text-gray-900":"text-red-900"),Z=q(()=>{const L=I.value-d.value;return L<=0?"You lead":`+${Ke(L)} ahead`}),oe=q(()=>{if(I.value===0)return 100;const L=d.value/I.value*100;return Math.min(Math.max(L,10),100)}),de=q(()=>R.value?"bg-gradient-to-r from-green-400 to-green-500":"bg-gradient-to-r from-amber-400 to-amber-500"),te=()=>{a.value=!0},V=()=>{var L;(L=r.bid.item)!=null&&L.id&&o("view-details",r.bid)},Y=()=>{o("increase-bid",r.bid)},ne=()=>{var L;(L=r.bid.item)!=null&&L.id&&(n.value=!0)};return(L,$)=>{var F,U,Q;return c(),p(me,null,[u.value?(c(),p("div",Sv,[e("div",Mv,[e("div",{class:J([M.value,"px-6 py-4"])},[e("div",Av,[e("div",jv,[e("div",{class:J([g.value,"p-2 rounded-lg"])},[(c(),p("svg",Tv,[R.value&&L.variant==="active"?(c(),p("path",Iv)):L.variant==="active"?(c(),p("path",Bv)):(c(),p("path",Ev))]))],2),e("div",null,[D(h(Qe),{variant:y.value,size:"sm",class:"text-white border-white/20"},{default:H(()=>[W(S(f.value),1)]),_:1},8,["variant"])])]),e("div",Nv,[e("div",Dv,S(L.variant==="active"?"Time Left":"Ended"),1),e("div",Pv,S(v.value),1)])])],2)]),e("div",Rv,[e("div",Lv,[e("div",zv,[e("div",Ov,[e("img",{src:l.value,alt:b.value,class:"w-24 h-24 object-cover group-hover:scale-105 transition-transform duration-300",onError:te},null,40,Fv),$[1]||($[1]=e("div",{class:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"},null,-1))]),x.value?(c(),p("div",Vv,$[2]||($[2]=[e("svg",{class:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)]))):z("",!0)]),e("div",qv,[e("div",Uv,[e("div",Hv,[e("h3",Wv,S(b.value),1),e("div",Kv,[e("span",Yv,S(w.value),1),(F=L.bid.item)!=null&&F.code?(c(),p("span",Qv," #"+S(L.bid.item.code),1)):z("",!0)])])]),e("div",Xv,[e("div",Gv,[$[3]||($[3]=e("div",{class:"flex items-center justify-between mb-2"},[e("span",{class:"text-xs font-bold text-blue-700 uppercase tracking-wide"},"Your Bid"),e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",Jv,S(h(Ke)(d.value)),1),e("div",Zv,S(T.value),1)]),e("div",{class:J(j.value)},[e("div",eg,[e("span",{class:J(["text-xs font-bold uppercase tracking-wide",A.value])},"Current Bid",2),(c(),p("svg",{class:J(["w-4 h-4",A.value]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},$[4]||($[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"},null,-1)]),2))]),e("div",{class:J(["text-xl font-bold",O.value])},S(h(Ke)(I.value)),3),e("div",{class:J(["text-xs mt-1",A.value])},S(Z.value),3)],2)]),L.variant==="active"?(c(),p("div",tg,[e("div",sg,[$[5]||($[5]=e("span",{class:"text-xs font-medium text-gray-600"},"Bid Position",-1)),e("span",{class:J(["text-xs font-semibold",R.value?"text-green-600":"text-amber-600"])},S(R.value?"Leading":"Behind"),3)]),e("div",rg,[e("div",{class:J([de.value,"h-2 rounded-full transition-all duration-500"]),style:rt({width:oe.value+"%"})},null,6)])])):z("",!0),e("div",og,[R.value&&L.variant==="active"?(c(),p("div",ag,$[6]||($[6]=[e("svg",{class:"w-5 h-5 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})],-1),e("div",null,[e("div",{class:"text-sm font-semibold text-green-800"},"You're currently winning!"),e("div",{class:"text-xs text-green-600"},"Keep monitoring for new bids")],-1)]))):L.variant==="active"?(c(),p("div",ng,$[7]||($[7]=[e("svg",{class:"w-5 h-5 text-amber-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1),e("div",null,[e("div",{class:"text-sm font-semibold text-amber-800"},"You've been outbid"),e("div",{class:"text-xs text-amber-600"},"Consider placing a higher bid")],-1)]))):L.variant==="history"?(c(),p("div",ig,[$[9]||($[9]=e("svg",{class:"w-5 h-5 text-gray-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[$[8]||($[8]=e("div",{class:"text-sm font-semibold text-gray-800"},"Auction completed",-1)),e("div",lg,S(B.value),1)])])):z("",!0)]),e("div",dg,[e("div",ug,[e("button",{onClick:V,class:"text-sm text-gray-600 hover:text-gray-900 transition-colors underline",disabled:!((U=L.bid.item)!=null&&U.id)}," View Details ",8,cg),e("button",{onClick:ne,class:"text-sm text-gray-600 hover:text-gray-900 transition-colors underline",disabled:!((Q=L.bid.item)!=null&&Q.id),title:"View bid history"}," History ",8,mg)]),L.variant==="active"&&_.value?(c(),ce(h(se),{key:0,onClick:Y,variant:"primary",size:"sm",class:"font-medium"},{default:H(()=>$[10]||($[10]=[W(" Increase Bid ")])),_:1,__:[10]})):z("",!0)])])])])])):(c(),p("div",fg,$[11]||($[11]=[ze('<div class="flex items-center justify-center text-gray-500"><svg class="w-8 h-8 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg><div><p class="text-sm font-medium">Invalid Bid Record</p><p class="text-xs">This bid is missing required information</p></div></div>',1)]))),D($v,{show:n.value,item:L.bid.item,onClose:$[0]||($[0]=ee=>n.value=!1)},null,8,["show","item"])],64)}}}),vg={class:"py-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50/50 transition-colors duration-200 group"},gg={class:"flex items-center space-x-3"},hg={class:"flex-shrink-0"},yg=["src","alt"],bg={class:"flex-1 min-w-0"},xg={class:"flex items-center justify-between"},wg={class:"flex-1 min-w-0"},_g={class:"text-sm font-medium text-gray-900 truncate"},kg={class:"flex items-center mt-1 space-x-3 text-xs text-gray-500"},Cg={key:0},$g={key:1},Sg={key:0,class:"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"},Mg=pe({__name:"WatchlistItemCard",props:{item:{},variant:{default:"default"}},emits:["view-details","remove-from-watchlist","place-bid"],setup(t,{emit:s}){const r=t,o=s,a=q(()=>{if(!r.item.date_to)return"";const b=new Date,w=new Date(r.item.date_to);return w<=b?"Ended":Tn(w)}),n=q(()=>{if(r.item.closed_by)return!1;if(r.item.date_to){const b=new Date;return new Date(r.item.date_to)>b}return!0}),u=()=>{o("place-bid",r.item)};return(b,w)=>{var l;return c(),p("div",vg,[e("div",gg,[e("div",hg,[e("img",{src:b.item.image||"/images/placeholder.jpg",alt:b.item.name,class:"w-12 h-12 object-cover rounded"},null,8,yg)]),e("div",bg,[e("div",xg,[e("div",wg,[e("h3",_g,S(b.item.name),1),e("div",kg,[e("span",null,S(((l=b.item.auctionType)==null?void 0:l.name)||"Unknown Type"),1),w[2]||(w[2]=e("span",null,"•",-1)),e("span",null,S(h(Ke)(b.item.bid_amount||0)),1),b.item.date_to?(c(),p("span",Cg,"•")):z("",!0),b.item.date_to?(c(),p("span",$g,S(a.value),1)):z("",!0)])]),e("button",{onClick:w[0]||(w[0]=d=>b.$emit("remove-from-watchlist",b.item)),class:"p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200 opacity-0 group-hover:opacity-100",title:"Remove from watchlist"},w[3]||(w[3]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),b.variant!=="compact"?(c(),p("div",Sg,[e("button",{onClick:w[1]||(w[1]=d=>b.$emit("view-details",b.item)),class:"text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200"}," View "),n.value?(c(),p("button",{key:0,onClick:u,class:"text-xs text-gray-900 hover:text-gray-700 font-medium transition-colors duration-200"}," Bid ")):z("",!0)])):z("",!0)])])])}}}),Ag={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},jg={class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},Tg={class:"py-6"},Ig={class:"flex items-center justify-between"},Bg={class:"flex items-center space-x-3"},Eg={key:0,class:"flex justify-center py-16"},Ng={class:"text-center"},Dg={key:1,class:"space-y-8"},Pg={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},Rg={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Lg={class:"flex items-center justify-between"},zg={class:"text-3xl font-bold text-gray-900 mt-2"},Og={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Fg={class:"flex items-center justify-between"},Vg={class:"text-3xl font-bold text-gray-900 mt-2"},qg={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Ug={class:"flex items-center justify-between"},Hg={class:"text-3xl font-bold text-gray-900 mt-2"},Wg={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Kg={class:"flex items-center justify-between"},Yg={class:"text-3xl font-bold text-gray-900 mt-2"},Qg={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Xg={class:"lg:col-span-2"},Gg={class:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"},Jg={class:"bg-gray-50 px-6 py-4 border-b border-gray-200"},Zg={class:"flex items-center justify-between"},eh={class:"p-6"},th={key:0,class:"text-center py-12"},sh={key:1},rh={class:"w-full"},oh={key:0,class:"text-center py-4 text-sm text-gray-500"},ah={class:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"},nh={class:"bg-gray-50 px-6 py-4 border-b border-gray-200"},ih={class:"flex items-center justify-between"},lh={key:0,class:"text-center py-12 px-6"},dh={key:1,class:"px-6"},uh={class:"bg-white rounded-xl shadow-lg border border-gray-200/50 overflow-hidden"},ch={class:"bg-gradient-to-r from-gray-50 to-slate-50 px-6 py-4 border-b border-gray-200/50"},mh={class:"flex items-center justify-between"},fh={class:"flex items-center space-x-2"},ph={class:"relative"},vh={class:"p-0"},gh={key:0,class:"text-center py-12 px-6"},hh={key:1},yh={key:0,class:"px-6 py-4 border-t border-gray-200"},js=pe({__name:"BidDashboard",setup(t){const s=ge(),r=qp(),o=nt(),a=Be(),n=qe(),u=q(()=>r.activeBids.length>0||r.bidHistory.length>0||o.items.length>0),b=q(()=>r.activeBids.filter(L=>L.item&&L.item.id)),w=q(()=>r.activeBids.length-b.value.length),l=P([]),d=P({current_page:1,last_page:1,per_page:10,total:0,from:0,to:0}),I=P(!1),B=P(""),T=q(()=>l.value.length>0?l.value:r.bidHistory.slice(0,10)),y=[{key:"item",label:"Item",sortable:!1,width:"300px",render:(L,$)=>{const F=oe($),U=de($);return`
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <img
              src="${Z($)}"
              alt="${F}"
              class="w-12 h-12 object-cover rounded-lg"
              onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+'"
            />
          </div>
          <div class="min-w-0 flex-1">
            <div class="text-sm font-medium text-gray-900 truncate">
              ${F}
            </div>
            <div class="text-sm text-gray-500 truncate">
              ${U}
            </div>
          </div>
        </div>
      `}},{key:"bid_amount",label:"Bid Amount",sortable:!0,align:"right",render:(L,$)=>{const F=parseFloat($.bid_amount||"0");return`
        <div class="text-right">
          <div class="text-sm font-semibold text-gray-900">
            ${Ke(F)}
          </div>
        </div>
      `}},{key:"status",label:"Status",sortable:!1,align:"center",render:(L,$)=>{const F=te($),U=V($);return`
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${F==="success"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}">
          ${U}
        </span>
      `}},{key:"created_at",label:"Date Placed",sortable:!0,render:(L,$)=>`
        <div class="text-sm text-gray-900">
          ${Fs($.created_at)}
        </div>
      `},{key:"actions",label:"",sortable:!1,align:"center",width:"60px",render:(L,$)=>{var ee;const F=!((ee=$.item)!=null&&ee.id),U=F?"inline-flex items-center justify-center w-8 h-8 rounded-full text-gray-400 bg-gray-100 cursor-not-allowed":"inline-flex items-center justify-center w-8 h-8 rounded-full text-gray-600 bg-gray-50 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 cursor-pointer transition-colors duration-200",Q=`
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
      `;return`
        <button
          class="${U}"
          onclick="window.handleViewBidDetails && window.handleViewBidDetails(${JSON.stringify($).replace(/"/g,"&quot;")})"
          ${F?"disabled":""}
          title="View Details"
        >
          ${Q}
        </button>
      `}}],f=async()=>{!r.activeBids.length&&!r.bidHistory.length&&await _()},_=async()=>{try{await o.initializeWatchlist();const[L,$]=await Promise.all([r.fetchDashboardData(),o.fetchWatchlist(),j(1)]);L||console.warn("Failed to load dashboard data"),$||console.warn("Failed to load watchlist data")}catch(L){console.error("Error loading dashboard data:",L),a.error("Failed to load dashboard data. Please try refreshing the page.")}},R=async()=>{await _(),a.success("Dashboard refreshed successfully")},M=L=>{var $;($=L.item)!=null&&$.id&&n.push(`/item/${L.item.id}`)},g=L=>{var $;($=L.item)!=null&&$.id&&n.push(`/item/${L.item.id}`)},v=L=>{n.push(`/item/${L.id}`)},x=async L=>{await o.removeFromWatchlist(L.id)?a.success(`${L.name} removed from watchlist`):a.error(o.error||"Failed to remove item from watchlist")},j=async(L=1)=>{I.value=!0;try{const $=await r.fetchBidHistory(L,d.value.per_page,B.value,"");l.value=$.data||[],d.value={current_page:$.current_page||1,last_page:$.last_page||1,per_page:$.per_page||10,total:$.total||0,from:$.from||0,to:$.to||0}}catch($){console.error("Failed to load bid history:",$),l.value=[]}finally{I.value=!1}},A=L=>{j(L)},O=()=>{clearTimeout(Y),Y=setTimeout(()=>{j(1)},500)},Z=L=>{var $,F;return(($=L.item)==null?void 0:$.image)||((F=L.item)==null?void 0:F.cropped)||"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+"},oe=L=>{var $;return(($=L.item)==null?void 0:$.name)||"Unknown Item"},de=L=>{var $;return(($=L.auctionType)==null?void 0:$.name)||"Unknown Type"},te=L=>L.closed_by===L.user_id?"success":"secondary",V=L=>L.closed_by===L.user_id?"Won":"Lost";let Y;const ne=async()=>{const L=window.user;L&&!s.user&&s.setUser(L),!s.user&&!s.isLoading&&await s.initialize()};return Te(async()=>{window.handleViewBidDetails=M,await ne(),s.isAuthenticated&&await _()}),Me(()=>s.isAuthenticated,async(L,$)=>{L&&!$?await _():!L&&$&&r.clearDashboard()},{immediate:!1}),(L,$)=>{const F=Zt("router-link");return c(),p("div",Ag,[D(is,{title:"Sign in to view your bid dashboard",message:"You need to be signed in to access your bidding activity and manage your watchlist.","fallback-style":"page",onAuthSuccess:f},{default:H(()=>[e("div",jg,[$[6]||($[6]=e("div",{class:"absolute inset-0 bg-gradient-to-b from-transparent to-black/5"},null,-1)),$[7]||($[7]=e("div",{class:"absolute inset-0"},[e("div",{class:"absolute top-2 left-4 w-12 h-12 bg-white/5 rounded-full blur-md"}),e("div",{class:"absolute bottom-2 right-4 w-16 h-16 bg-white/8 rounded-full blur-lg"}),e("div",{class:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl"}),e("div",{class:"absolute inset-0 opacity-5",style:{"background-image":"radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)","background-size":"20px 20px"}})],-1)),D(h($e),{class:"relative z-10"},{default:H(()=>[e("div",Tg,[e("div",Ig,[$[5]||($[5]=e("div",{class:"flex items-center space-x-4"},[e("div",{class:"flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])]),e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Bid Dashboard "),e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-slate-300"},[e("li",null,[e("a",{href:"/",class:"hover:text-white transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Bid Dashboard")])])])],-1)),e("div",Bg,[D(h(se),{onClick:R,loading:h(r).isLoading,variant:"outline",size:"sm",class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:H(()=>$[4]||($[4]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),W(" Refresh ")])),_:1,__:[4]},8,["loading"])])])])]),_:1})]),D(h($e),{class:"py-8"},{default:H(()=>[h(r).isLoading&&!u.value?(c(),p("div",Eg,[e("div",Ng,[D(h(Ue),{size:"lg"}),$[8]||($[8]=e("p",{class:"mt-4 text-sm text-gray-500"},"Loading your dashboard...",-1))])])):(c(),p("div",Dg,[e("div",Pg,[e("div",Rg,[e("div",Lg,[e("div",null,[$[9]||($[9]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Active Bids",-1)),e("p",zg,S(h(r).stats.active_bids),1),$[10]||($[10]=e("p",{class:"text-gray-500 text-sm mt-1"},"Currently bidding",-1))]),$[11]||($[11]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Og,[e("div",Fg,[e("div",null,[$[12]||($[12]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Won Auctions",-1)),e("p",Vg,S(h(r).stats.won_auctions),1),$[13]||($[13]=e("p",{class:"text-gray-500 text-sm mt-1"},"Successful bids",-1))]),$[14]||($[14]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})])],-1))])]),e("div",qg,[e("div",Ug,[e("div",null,[$[15]||($[15]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Watchlist Items",-1)),e("p",Hg,S(h(r).stats.watchlist_count),1),$[16]||($[16]=e("p",{class:"text-gray-500 text-sm mt-1"},"Items watching",-1))]),$[17]||($[17]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])],-1))])]),e("div",Wg,[e("div",Kg,[e("div",null,[$[18]||($[18]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Win Rate",-1)),e("p",Yg,S(h(r).stats.win_rate)+"%",1),$[19]||($[19]=e("p",{class:"text-gray-500 text-sm mt-1"},"Success rate",-1))]),$[20]||($[20]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])])]),e("div",Qg,[e("div",Xg,[e("div",Gg,[e("div",Jg,[e("div",Zg,[$[22]||($[22]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"bg-gray-600 p-2 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("h2",{class:"text-lg font-semibold text-gray-900"},"Active Bids")],-1)),D(F,{to:"/bid-dashboard/active-bids",class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"},{default:H(()=>$[21]||($[21]=[W(" View all "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)])),_:1,__:[21]})])]),e("div",eh,[h(r).activeBids.length===0?(c(),p("div",th,[$[24]||($[24]=e("div",{class:"bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),$[25]||($[25]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No active bids",-1)),$[26]||($[26]=e("p",{class:"text-gray-500 mb-4"},"Start bidding on items to see them here.",-1)),D(h(se),{onClick:$[0]||($[0]=U=>L.$router.push("/")),variant:"primary",size:"sm"},{default:H(()=>$[23]||($[23]=[W(" Browse Auctions ")])),_:1,__:[23]})])):(c(),p("div",sh,[D(h(jn),{items:b.value,"auto-play":!1,"show-counter":!0,"container-classes":"rounded-lg","navigation-button-classes":"hover:bg-gray-50","dot-classes":"hover:scale-105","key-extractor":U=>U.id},{default:H(({item:U,isActive:Q})=>[e("div",rh,[D(pg,{bid:U,variant:"active",onViewDetails:M,onIncreaseBid:g},null,8,["bid"])])]),_:1},8,["items","key-extractor"]),w.value>0?(c(),p("div",oh,S(w.value)+" incomplete bid record"+S(w.value>1?"s":"")+" hidden ",1)):z("",!0)]))])])]),e("div",null,[e("div",ah,[e("div",nh,[e("div",ih,[$[28]||($[28]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"bg-gray-600 p-2 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])]),e("h2",{class:"text-lg font-semibold text-gray-900"},"Watchlist")],-1)),D(F,{to:"/bid-dashboard/watchlist",class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"},{default:H(()=>$[27]||($[27]=[W(" View all "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)])),_:1,__:[27]})])]),e("div",null,[h(o).isEmpty?(c(),p("div",lh,[$[30]||($[30]=e("div",{class:"bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])],-1)),$[31]||($[31]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No watched items",-1)),$[32]||($[32]=e("p",{class:"text-gray-500 mb-4"},"Add items to your watchlist to track them.",-1)),D(h(se),{onClick:$[1]||($[1]=U=>L.$router.push("/")),variant:"outline",size:"sm"},{default:H(()=>$[29]||($[29]=[W(" Browse Items ")])),_:1,__:[29]})])):(c(),p("div",dh,[(c(!0),p(me,null,fe(h(o).items.slice(0,5),U=>(c(),ce(Mg,{key:U.id,item:U,variant:"compact",onViewDetails:v,onPlaceBid:v,onRemoveFromWatchlist:x},null,8,["item"]))),128))]))])])])]),e("div",uh,[e("div",ch,[e("div",mh,[$[34]||($[34]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"bg-gray-600 p-2 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])]),e("h2",{class:"text-lg font-semibold text-gray-900"},"Recent Bid History")],-1)),e("div",fh,[e("div",ph,[D(h(be),{modelValue:B.value,"onUpdate:modelValue":$[2]||($[2]=U=>B.value=U),placeholder:"Search history...",size:"sm",class:"w-48",onInput:O},null,8,["modelValue"]),$[33]||($[33]=e("svg",{class:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])])])]),e("div",vh,[T.value.length===0&&!I.value?(c(),p("div",gh,[$[36]||($[36]=e("div",{class:"bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])],-1)),$[37]||($[37]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No bid history",-1)),$[38]||($[38]=e("p",{class:"text-gray-500 mb-4"},"Your completed bids will appear here.",-1)),D(h(se),{onClick:$[3]||($[3]=U=>L.$router.push("/")),variant:"outline",size:"sm"},{default:H(()=>$[35]||($[35]=[W(" Start Bidding ")])),_:1,__:[35]})])):(c(),p("div",hh,[D(h(ko),{columns:y,data:T.value,loading:I.value,bordered:!1,striped:!0,hover:!0,class:"min-h-[400px]"},null,8,["data","loading"]),d.value.last_page>1&&l.value.length>0?(c(),p("div",yh,[D(h(Rs),{"current-page":d.value.current_page,"total-pages":d.value.last_page,total:d.value.total,"per-page":d.value.per_page,"show-info":!0,"show-page-size":!1,onPageChange:A},null,8,["current-page","total-pages","total","per-page"])])):z("",!0)]))])])]))]),_:1})]),_:1})])}}}),bh=()=>{const t=window.location.pathname;return t.startsWith("/spa")?[{path:"/",name:"spa-homepage",component:Pt,meta:{title:"Vertigo AMS - Auction Management System",requiresAuth:!1}},{path:"/item/:id",name:"spa-item-detail",component:Ot,meta:{title:"Item Details - Vertigo AMS",requiresAuth:!1}},{path:"/cart",name:"spa-cart",component:Rt,meta:{title:"Shopping Cart - Vertigo AMS",requiresAuth:!1}},{path:"/checkout",name:"spa-checkout",component:Lt,meta:{title:"Secure Checkout - Vertigo AMS",requiresAuth:!1}},{path:"/payment-success",name:"spa-payment-success",component:zt,meta:{title:"Payment Successful - Vertigo AMS",requiresAuth:!1}},{path:"/register-bid",name:"spa-register-bid",component:As,meta:{title:"Register for Auction - Vertigo AMS",requiresAuth:!1}},{path:"/bid-dashboard",name:"spa-bid-dashboard",component:js,meta:{title:"Bid Dashboard - Vertigo AMS",requiresAuth:!0}},{path:"/:pathMatch(.*)*",name:"spa-not-found",component:Pt,meta:{title:"Page Not Found - Vertigo AMS",requiresAuth:!1}}]:t==="/cart"?[{path:"/",name:"cart",component:Rt,meta:{title:"Shopping Cart - Vertigo AMS",requiresAuth:!1}}]:t==="/checkout"?[{path:"/",name:"checkout",component:Lt,meta:{title:"Secure Checkout - Vertigo AMS",requiresAuth:!1}}]:t==="/payment-success"?[{path:"/",name:"payment-success",component:zt,meta:{title:"Payment Successful - Vertigo AMS",requiresAuth:!1}}]:t.startsWith("/item/")?[{path:"/",name:"item-detail",component:Ot,meta:{title:"Item Details - Vertigo AMS",requiresAuth:!1}}]:[{path:"/",name:"homepage",component:Pt,meta:{title:"Vertigo AMS - Auction Management System",requiresAuth:!1}},{path:"/item/:id",name:"item-detail",component:Ot,meta:{title:"Item Details - Vertigo AMS",requiresAuth:!1}},{path:"/cart",name:"cart",component:Rt,meta:{title:"Shopping Cart - Vertigo AMS",requiresAuth:!1}},{path:"/checkout",name:"checkout",component:Lt,meta:{title:"Secure Checkout - Vertigo AMS",requiresAuth:!1}},{path:"/payment-success",name:"payment-success",component:zt,meta:{title:"Payment Successful - Vertigo AMS",requiresAuth:!1}},{path:"/register-bid",name:"register-bid",component:As,meta:{title:"Register for Auction - Vertigo AMS",requiresAuth:!1}},{path:"/bid-dashboard",name:"bid-dashboard",component:js,meta:{title:"Bid Dashboard - Vertigo AMS",requiresAuth:!0}}]},xh=bh(),wh=()=>{const t=window.location.pathname;return t.startsWith("/spa")?"/spa":t.startsWith("/home-vue")?"/home-vue":t.startsWith("/cart")?"/cart":t.startsWith("/checkout")?"/checkout":t.startsWith("/payment-success")?"/payment-success":t.startsWith("/register-bid")?"/register-bid":(t.startsWith("/item/"),"/")},yr=Ar({history:jr(wh()),routes:xh,scrollBehavior(t,s,r){return r||{top:0}}});yr.beforeEach(async(t,s,r)=>{t.meta.title&&(document.title=t.meta.title);const o=ge();if(!o.user)try{await o.initialize()}catch(a){console.error("Failed to initialize auth:",a)}if(t.meta.requiresAuth){const a=_h();if(console.log("Router guard: isAuthenticated =",a,"user =",o.user,"sessionAuth =",o.sessionAuth),!a&&!o.user)if(window.location.pathname.startsWith("/spa")){console.log("Allowing navigation to continue, will check auth in component"),r();return}else{window.location.href="/login?redirect="+encodeURIComponent(t.fullPath);return}}if(t.meta.requiresAdmin&&!kh()){console.warn("Admin access required for this route"),r("/");return}r()});function _h(){const t=ge(),s=t.sessionAuth||t.user&&!t.token,r=t.token&&t.user;return s||r||t.isAuthenticated}function kh(){var r;const t=ge();if(!t.isAuthenticated)return!1;const s=t.user;return((r=s==null?void 0:s.roles)==null?void 0:r.some(o=>o.name==="admin"))||!1}const Ch={template:`
    <div class="min-h-screen bg-gray-50">
      <router-view />
      <NotificationContainer />

      <!-- Global Auth Modal -->
      <AuthModal
        v-model:show="showAuthModal"
        :start-with-register="authModalTab === 'register'"
        title="Welcome to Vertigo AMS"
        subtitle="Your premier auction management system"
        @success="handleAuthSuccess"
        @close="hideAuthModal"
      />
    </div>
  `,setup(){const t=Be(),s=ge(),r=qe(),o=P(!1),a=P("login"),n=w=>{var l;a.value=((l=w.detail)==null?void 0:l.tab)||"login",o.value=!0},u=()=>{o.value=!1},b=w=>{o.value=!1;const l=(w==null?void 0:w.name)||(w==null?void 0:w.email)||"there";t.success(`Welcome back, ${l}! You have successfully signed in.`);const d=sessionStorage.getItem("auth_redirect");d?(sessionStorage.removeItem("auth_redirect"),r.push(d)):window.location.reload()};return Te(async()=>{window.addEventListener("show-auth-modal",n);try{await s.initialize()}catch(w){console.error("Failed to initialize auth store:",w)}}),Jt(()=>{window.removeEventListener("show-auth-modal",n)}),{showAuthModal:o,authModalTab:a,hideAuthModal:u,handleAuthSuccess:b}}},ve=kr(Ch),$h=Cr();ve.use($h);ve.use(yr);ve.component("AppLayout",$d);ve.component("Button",se);ve.component("Input",be);ve.component("Card",et);ve.component("Modal",Es);ve.component("Select",Ps);ve.component("Loading",Ue);ve.component("Container",$e);ve.component("ItemCard",pr);ve.component("Banner",Os);ve.component("Badge",Qe);ve.component("Alert",Ft);ve.component("Pagination",Rs);ve.component("NotificationContainer",Ds);ve.component("CartDrawer",vr);ve.component("CartIcon",Yd);ve.component("CartNavIcon",Xd);ve.component("WatchlistNavIcon",Ra);ve.component("AuthModal",yt);const Xt=ge(),Sh=gr(),Mh=hr(),Ts=Le(),Gt=nt();Be();Xt.initialize().then(()=>{Xt.isAuthenticated&&Gt.initializeWatchlist()});Me(()=>Xt.isAuthenticated,async t=>{t?await Gt.initializeWatchlist():Gt.clearWatchlist()},{immediate:!1});window.branches&&Sh.initializeBranches(window.branches);window.adverts&&Mh.initializeAdverts(window.adverts);window.cartItems?Ts.initializeCart(window.cartItems):Ts.fetchCart();ve.mount("#app");const Ah=async()=>{const t=document.getElementById("watchlist-nav-icon");if(t){const s=ge();t.addEventListener("click",r=>{r.preventDefault(),s.isAuthenticated?window.location.href="/bid-dashboard":window.dispatchEvent(new CustomEvent("show-auth-modal",{detail:{tab:"login"}}))})}};setTimeout(async()=>{await Ah()},100);ve.config.errorHandler=(t,s,r)=>{console.error("Vue error:",t,r)};ve.config.warnHandler=(t,s,r)=>{console.warn("Vue warning:",t,r)};
