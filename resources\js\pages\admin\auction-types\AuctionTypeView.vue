<template>
  <AdminDetailTemplate
    :title="auctionType?.name || 'Auction Type Details'"
    :subtitle="`${formatType(auctionType?.type)} - ${auctionType?.is_active !== false ? 'Active' : 'Inactive'}`"
    :loading="loading"
    :error="error"
    :breadcrumbs="breadcrumbs"
  >
    <template #actions>
      <div class="flex space-x-3">
        <Button
          variant="outline"
          @click="handleEdit"
          :disabled="!auctionType"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Edit Auction Type
        </Button>
        <Button
          variant="outline"
          @click="handleDelete"
          :disabled="!auctionType || (auctionType.items_count || 0) > 0"
          class="text-red-600 hover:text-red-700"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Delete Auction Type
        </Button>
      </div>
    </template>

    <div v-if="auctionType" class="space-y-6">
      <!-- Status and Type -->
      <Card class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-medium text-gray-900">Status & Type</h3>
            <p class="text-sm text-gray-500 mt-1">Current auction type configuration</p>
          </div>
          <div class="flex space-x-3">
            <AdminBadge
              :variant="getTypeBadgeVariant(auctionType.type)"
              size="lg"
            >
              {{ formatType(auctionType.type) }}
            </AdminBadge>
            <AdminBadge
              :variant="auctionType.is_active !== false ? 'success' : 'secondary'"
              size="lg"
            >
              {{ auctionType.is_active !== false ? 'Active' : 'Inactive' }}
            </AdminBadge>
          </div>
        </div>
      </Card>

      <!-- Basic Information -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Name</label>
            <p class="mt-1 text-sm text-gray-900">{{ auctionType.name }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Type</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatType(auctionType.type) }}</p>
          </div>

          <div v-if="auctionType.description" class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700">Description</label>
            <p class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{{ auctionType.description }}</p>
          </div>
        </div>
      </Card>

      <!-- Statistics -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ auctionType.items_count || 0 }}</div>
            <div class="text-sm text-gray-500">Associated Items</div>
          </div>

          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ auctionType.auctions_count || 0 }}</div>
            <div class="text-sm text-gray-500">Total Auctions</div>
          </div>

          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">{{ formatDate(auctionType.created_at) }}</div>
            <div class="text-sm text-gray-500">Created Date</div>
          </div>
        </div>
      </Card>

      <!-- Associated Items -->
      <Card v-if="associatedItems.length > 0" class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">Associated Items</h3>
          <Button
            variant="outline"
            size="sm"
            @click="viewAllItems"
          >
            View All Items
          </Button>
        </div>
        
        <div class="space-y-3">
          <div
            v-for="item in displayedItems"
            :key="item.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors cursor-pointer"
            @click="viewItem(item)"
          >
            <div class="flex items-center space-x-3">
              <img
                :src="item.image || '/img/product.jpeg'"
                :alt="item.name"
                class="w-12 h-12 rounded-lg object-cover"
              />
              <div>
                <p class="text-sm font-medium text-gray-900">{{ item.name }}</p>
                <p class="text-xs text-gray-500">Ref: {{ item.reference_number || 'N/A' }}</p>
                <p class="text-xs text-gray-400">Target: {{ formatCurrency(item.target_amount) }}</p>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <AdminBadge
                :variant="item.closed_by ? 'success' : 'warning'"
                size="sm"
              >
                {{ item.closed_by ? 'Sold' : 'Available' }}
              </AdminBadge>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>

        <div v-if="associatedItems.length > 5" class="mt-4 text-center">
          <Button
            variant="outline"
            size="sm"
            @click="showAllItems = !showAllItems"
          >
            {{ showAllItems ? 'Show Less' : `Show All ${totalAssociatedItems} Items` }}
          </Button>
        </div>
      </Card>

      <!-- No Items State -->
      <Card v-else class="p-6">
        <div class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No associated items</h3>
          <p class="mt-1 text-sm text-gray-500">This auction type doesn't have any items yet.</p>
          <div class="mt-6">
            <Button variant="outline" @click="createItem">
              Add First Item
            </Button>
          </div>
        </div>
      </Card>

      <!-- Images -->
      <Card v-if="auctionType.media && auctionType.media.length > 0" class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Images</h3>
        
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <div
            v-for="(image, index) in auctionType.media"
            :key="image.id"
            class="relative group cursor-pointer"
            @click="openImageModal(image, index)"
          >
            <img
              :src="image.url"
              :alt="`Image ${index + 1}`"
              class="w-full h-24 object-cover rounded-lg border hover:opacity-75 transition-opacity"
            />
            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>
      </Card>

      <!-- Timestamps -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Timestamps</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Created</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(auctionType.created_at) }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Last Updated</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(auctionType.updated_at) }}</p>
          </div>
        </div>
      </Card>
    </div>
  </AdminDetailTemplate>

  <!-- Image Modal -->
  <AdminModal v-if="showImageModal && selectedImage" @close="closeImageModal" size="lg">
    <template #header>
      <h3 class="text-lg font-medium">{{ auctionType?.name }} - Image {{ selectedImageIndex + 1 }}</h3>
    </template>
    
    <div class="space-y-4">
      <img
        :src="selectedImage.url"
        :alt="`Image ${selectedImageIndex + 1}`"
        class="w-full max-h-96 object-contain rounded-lg"
      />
      
      <!-- Image Navigation -->
      <div v-if="auctionType?.media && auctionType.media.length > 1" class="flex justify-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          @click="previousImage"
          :disabled="selectedImageIndex === 0"
        >
          Previous
        </Button>
        <span class="flex items-center text-sm text-gray-500">
          {{ selectedImageIndex + 1 }} of {{ auctionType.media.length }}
        </span>
        <Button
          variant="outline"
          size="sm"
          @click="nextImage"
          :disabled="selectedImageIndex === auctionType.media.length - 1"
        >
          Next
        </Button>
      </div>
    </div>
    
    <template #footer>
      <div class="flex justify-end space-x-3">
        <Button variant="outline" @click="closeImageModal">Close</Button>
        <Button variant="primary" @click="downloadImage">Download</Button>
      </div>
    </template>
  </AdminModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { AdminDetailTemplate, AdminBadge, AdminModal } from '@/components/admin/templates';
import { Card, Button } from '@/components/ui';
import { useAdminAuctionTypes } from '@/stores/admin/auctionTypes';
import { useAdminItems } from '@/stores/admin/items';
import { useNotifications } from '@/composables/useNotifications';

// Router
const router = useRouter();
const route = useRoute();

// Stores
const auctionTypesStore = useAdminAuctionTypes();
const itemsStore = useAdminItems();

// Notifications
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const associatedItems = ref<any[]>([]);
const totalAssociatedItems = ref(0);
const showAllItems = ref(false);
const showImageModal = ref(false);
const selectedImage = ref<any>(null);
const selectedImageIndex = ref(0);

// Computed
const auctionTypeId = computed(() => route.params.id ? parseInt(route.params.id as string) : null);
const auctionType = computed(() => auctionTypesStore.currentAuctionType);

const breadcrumbs = computed(() => [
  { label: 'Dashboard', href: '/admin-spa' },
  { label: 'Auction Types', href: '/admin-spa/auction-types/list' },
  { label: auctionType.value?.name || 'Auction Type Details' }
]);

const displayedItems = computed(() => {
  return showAllItems.value ? associatedItems.value : associatedItems.value.slice(0, 5);
});

// Methods
const loadAuctionType = async () => {
  if (!auctionTypeId.value) return;

  loading.value = true;
  error.value = null;

  try {
    await auctionTypesStore.fetchAuctionType(auctionTypeId.value);
    await loadAssociatedItems();
  } catch (err) {
    error.value = 'Failed to load auction type';
    showNotification('Failed to load auction type', 'error');
  } finally {
    loading.value = false;
  }
};

const loadAssociatedItems = async () => {
  if (!auctionTypeId.value) return;

  try {
    await itemsStore.fetchItems({
      auction_type_id: auctionTypeId.value.toString(),
      per_page: showAllItems.value ? undefined : 5
    });

    associatedItems.value = itemsStore.itemsList;
    totalAssociatedItems.value = itemsStore.totalItems;
  } catch (err) {
    console.error('Failed to load associated items:', err);
  }
};

const handleEdit = () => {
  if (auctionTypeId.value) {
    router.push(`/admin-spa/auction-types/edit/${auctionTypeId.value}`);
  }
};

const handleDelete = async () => {
  if (!auctionType.value) return;

  if ((auctionType.value.items_count || 0) > 0) {
    showNotification('Cannot delete auction type with associated items', 'error');
    return;
  }

  if (confirm(`Are you sure you want to delete "${auctionType.value.name}"?`)) {
    try {
      await auctionTypesStore.deleteAuctionType(auctionType.value.id);
      showNotification('Auction type deleted successfully', 'success');
      router.push('/admin-spa/auction-types/list');
    } catch (error) {
      showNotification('Failed to delete auction type', 'error');
    }
  }
};

const viewItem = (item: any) => {
  router.push(`/admin-spa/items/view/${item.id}`);
};

const viewAllItems = () => {
  router.push(`/admin-spa/items/list?auction_type_id=${auctionTypeId.value}`);
};

const createItem = () => {
  router.push(`/admin-spa/items/create?auction_type_id=${auctionTypeId.value}`);
};

// Image modal methods
const openImageModal = (image: any, index: number) => {
  selectedImage.value = image;
  selectedImageIndex.value = index;
  showImageModal.value = true;
};

const closeImageModal = () => {
  showImageModal.value = false;
  selectedImage.value = null;
  selectedImageIndex.value = 0;
};

const previousImage = () => {
  if (selectedImageIndex.value > 0) {
    selectedImageIndex.value--;
    selectedImage.value = auctionType.value?.media[selectedImageIndex.value];
  }
};

const nextImage = () => {
  if (auctionType.value?.media && selectedImageIndex.value < auctionType.value.media.length - 1) {
    selectedImageIndex.value++;
    selectedImage.value = auctionType.value.media[selectedImageIndex.value];
  }
};

const downloadImage = () => {
  if (selectedImage.value?.url) {
    const link = document.createElement('a');
    link.href = selectedImage.value.url;
    link.download = `${auctionType.value?.name || 'image'}-${selectedImageIndex.value + 1}`;
    link.click();
  }
};

// Utility methods
const getTypeBadgeVariant = (type: string) => {
  switch (type) {
    case 'live': return 'success';
    case 'online': return 'primary';
    case 'cash': return 'warning';
    default: return 'secondary';
  }
};

const formatType = (type: string | undefined) => {
  if (!type) return 'Unknown';
  switch (type) {
    case 'live': return 'Live Auction';
    case 'online': return 'Online Auction';
    case 'cash': return 'Cash Sale';
    default: return type;
  }
};

const formatCurrency = (amount: number | null | undefined) => {
  if (!amount) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatDate = (date: string | null | undefined) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString();
};

const formatDateTime = (date: string | null | undefined) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleString();
};

// Lifecycle
onMounted(() => {
  loadAuctionType();
});
</script>
