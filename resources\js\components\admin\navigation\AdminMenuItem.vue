<template>
  <div class="menu-item-wrapper">
    <!-- Main Menu Item -->
    <div
      :class="menuItemClasses"
      :role="item.hasChildren ? 'button' : 'link'"
      :tabindex="0"
      :aria-expanded="item.hasChildren ? item.expanded : undefined"
      @click="handleClick"
      @keydown.enter.prevent="handleClick"
      @keydown.space.prevent="handleClick"
      v-tooltip="collapsed ? item.label : ''"
    >
      <div class="flex items-center flex-1">
        <!-- Icon -->
        <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
        </svg>
        
        <!-- Label -->
        <span v-if="!collapsed" class="flex-1">{{ item.label }}</span>
      </div>
      
      <!-- Badge -->
      <span v-if="item.badge && !collapsed" class="px-2 py-0.5 text-xs font-semibold text-white bg-primary-500 rounded-full">
        {{ item.badge }}
      </span>

      <!-- Dropdown Arrow -->
      <svg 
        v-if="item.hasChildren && !collapsed" 
        :class="dropdownArrowClasses" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </div>

    <!-- Submenu -->
    <transition name="slide-fade">
      <div v-if="item.hasChildren && item.expanded && !collapsed" class="ml-8 mt-2 space-y-1">
        <router-link
          v-for="child in item.children"
          :key="child.key"
          :to="child.route"
          :class="submenuItemClasses(child)"
          @click.stop
        >
          <span class="flex-1">{{ child.label }}</span>
          <span v-if="child.badge" class="px-2 py-0.5 text-xs font-semibold text-white bg-primary-500 rounded-full">
            {{ child.badge }}
          </span>
        </router-link>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAdminStore } from '@/stores/admin';
import { useAdminNavigation, type NavigationItem } from '@/composables/admin/useAdminNavigation';

interface Props {
  item: NavigationItem;
  collapsed: boolean;
}

const props = defineProps<Props>();

const adminStore = useAdminStore();
const route = useRoute();
const router = useRouter();
const { toggleMenu, isParentActive } = useAdminNavigation();

const isActive = computed(() => {
  if (props.item.route) {
    return route.path === props.item.route;
  }
  return isParentActive(props.item);
});

const menuItemClasses = computed(() => [
  'w-full flex items-center justify-between px-3 py-3 rounded-lg font-medium transition-all duration-200 mobile-touch-target cursor-pointer',
  isActive.value
    ? 'bg-primary-100 text-primary-800'
    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100',
]);

const dropdownArrowClasses = computed(() => [
  'h-4 w-4 transition-transform duration-200',
  props.item.expanded ? 'rotate-180' : ''
]);

const submenuItemClasses = (child: any) => [
  'flex items-center justify-between px-3 py-3 text-sm rounded-md mobile-touch-target transition-colors duration-200',
  route.path === child.route
    ? 'bg-white text-primary-700 font-medium shadow-sm'
    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50',
];

const handleClick = () => {
  if (props.item.hasChildren) {
    if (!props.collapsed) {
      toggleMenu(props.item.key);
    }
  } else if (props.item.route) {
    router.push(props.item.route);
    adminStore.closeMobileMenu();
  }
};
</script>

<style scoped>
.menu-item-wrapper {
  transition: all 0.2s ease;
}

.mobile-touch-target {
  min-height: 44px;
  min-width: 44px;
  -webkit-tap-highlight-color: rgba(0, 104, 255, 0.1);
}

.slide-fade-enter-active, .slide-fade-leave-active {
  transition: all 0.3s ease;
  max-height: 200px; /* Adjust as needed */
  overflow: hidden;
}
.slide-fade-enter-from, .slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
  max-height: 0;
}
</style>
