import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';

export const useAdminStore = defineStore('admin', () => {
  // --- STATE ---
  const sidebarCollapsed = ref(false);
  const mobileMenuOpen = ref(false);

  // Default state for all menus. This ensures all keys exist from the start.
  const openMenus = reactive<Record<string, boolean>>({
    dashboard: true,
    sales: false,
    auctions: false,
    items: false,
    users: false,
    financial: false,
    reports: false,
    settings: false,
  });

  // --- ACTIONS ---
  const initializeFromLocalStorage = () => {
    const savedSidebarState = localStorage.getItem('admin-sidebar-collapsed');
    if (savedSidebarState) {
      sidebarCollapsed.value = JSON.parse(savedSidebarState);
    }

    const savedOpenMenus = localStorage.getItem('admin-open-menus');
    if (savedOpenMenus) {
      try {
        // Merge saved state with defaults, ensuring no keys are missing
        Object.assign(openMenus, JSON.parse(savedOpenMenus));
      } catch (e) {
        console.error("Failed to parse open menus from localStorage", e);
      }
    }
  };

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value;
    localStorage.setItem('admin-sidebar-collapsed', JSON.stringify(sidebarCollapsed.value));
  };

  const toggleMenu = (menuKey: string) => {
    if (openMenus.hasOwnProperty(menuKey)) {
      openMenus[menuKey] = !openMenus[menuKey];
      localStorage.setItem('admin-open-menus', JSON.stringify(openMenus));
    }
  };
  
  const toggleMobileMenu = () => {
    mobileMenuOpen.value = !mobileMenuOpen.value;
  };

  const closeMobileMenu = () => {
    mobileMenuOpen.value = false;
  };

  // --- INITIALIZATION ---
  // Load state from localStorage as soon as the store is created.
  initializeFromLocalStorage();

  return {
    sidebarCollapsed,
    mobileMenuOpen,
    openMenus,
    toggleSidebar,
    toggleMenu,
    toggleMobileMenu,
    closeMobileMenu,
  };
});

// Re-export all admin stores
export { useAdminDashboard } from './dashboard';
export { useAdminUsers } from './users';
export { useAdminAuctions } from './auctions';
export { useAdminSettings } from './settings';
