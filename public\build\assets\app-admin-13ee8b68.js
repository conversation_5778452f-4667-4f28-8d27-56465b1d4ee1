import{s as ue,r as z,R as K,h as L,i as b,L as ee,M as te,o as c,f as m,g as e,t as v,j as $,n as I,O as U,B as se,F as P,k as E,m as W,x as _,l as me,P as Z,H as C,v as p,u as n,p as G,C as Q,D as Y,q as pe,_ as j,K as ve,T as X,y as fe,d as H,b as ae,c as ge,a as he,e as be}from"./Modal.vue_vue_type_script_setup_true_lang-79d5f001.js";import{f as D,b as q,_ as O,u as F,a as J,j as ye,k as xe,h as ke,L as _e,g as we,e as Me,A as Ce,i as Ae,N as Se}from"./Container.vue_vue_type_script_setup_true_lang-6937fcf6.js";const T=ue("admin",()=>{const l=z(!1),t=z(!1),a=z(!1),d=K({auctions:!1,items:!1,users:!1,financial:!1,reports:!1,settings:!1}),u=K({theme:"light",density:"comfortable",language:"en"}),g=z(!1),k=()=>{const f=localStorage.getItem("admin-preferences");if(f)try{const V=JSON.parse(f);Object.assign(u,V)}catch(V){console.error("Failed to parse admin preferences:",V)}const S=localStorage.getItem("admin-sidebar-collapsed");S!==null&&(l.value=JSON.parse(S));const N=localStorage.getItem("admin-open-menus");if(N)try{const V=JSON.parse(N);Object.assign(d,V)}catch(V){console.error("Failed to parse admin open menus:",V)}g.value=!0},w=()=>{l.value=!l.value,l.value&&Object.keys(d).forEach(f=>{d[f]=!1}),localStorage.setItem("admin-sidebar-collapsed",JSON.stringify(l.value))},s=()=>{t.value=!t.value,t.value?document.body.classList.add("overflow-hidden"):document.body.classList.remove("overflow-hidden")},y=()=>{t.value=!1,document.body.classList.remove("overflow-hidden")},M=()=>{a.value=!a.value},A=()=>{a.value=!1},r=f=>{l.value||(d[f]=!d[f],localStorage.setItem("admin-open-menus",JSON.stringify(d)))},h=()=>{Object.keys(d).forEach(f=>{d[f]=!1}),localStorage.setItem("admin-open-menus",JSON.stringify(d))};return{sidebarCollapsed:l,mobileMenuOpen:t,notificationsOpen:a,openMenus:d,preferences:u,isInitialized:g,initialize:k,toggleSidebar:w,toggleMobileMenu:s,closeMobileMenu:y,toggleNotifications:M,closeNotifications:A,toggleMenu:r,closeAllMenus:h,updatePreferences:f=>{Object.assign(u,f),localStorage.setItem("admin-preferences",JSON.stringify(u))},reset:()=>{l.value=!1,t.value=!1,a.value=!1,h(),u.theme="light",u.density="comfortable",u.language="en",localStorage.removeItem("admin-preferences"),localStorage.removeItem("admin-sidebar-collapsed"),localStorage.removeItem("admin-open-menus")}}}),$e={class:"menu-item"},Ie=["role","tabindex","aria-expanded","onKeydown"],je={class:"flex items-center"},ze={class:"h-5 w-5 mr-3 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ne=["d"],Ve={key:0},Le={key:0,class:"ml-8 mt-2 space-y-1"},Te=L({__name:"AdminMenuItem",props:{item:{},collapsed:{type:Boolean}},setup(l){const t=l,a=T(),d=D(),u=q(),g=z(!1),k=z({}),w=b(()=>{const o=["w-full flex items-center justify-between px-3 py-3 rounded-lg font-medium transition-all duration-200 mobile-touch-target"];return t.item.active||y.value?o.push("bg-white text-primary-700 border border-primary-200 shadow-sm"):o.push("text-gray-600 hover:text-gray-900 hover:bg-gray-50"),o.join(" ")}),s=b(()=>["h-4 w-4 transition-transform duration-200",t.item.expanded?"rotate-180":""]),y=b(()=>t.item.route?d.path===t.item.route:t.item.children?t.item.children.some(o=>d.path===o.route):!1),M=o=>{const i=["block px-3 py-3 text-sm rounded-md mobile-touch-target transition-colors duration-200"];return d.path===o.route?i.push("text-primary-700 bg-white font-medium border border-primary-200 shadow-sm"):i.push("text-gray-600 hover:text-gray-900 hover:bg-gray-50"),i.join(" ")},A=o=>{t.item.hasChildren?(o.preventDefault(),t.collapsed||a.toggleMenu(t.item.key)):t.item.route&&u.push(t.item.route)},r=o=>{if(t.collapsed){const i=o.target.getBoundingClientRect();k.value={left:`${i.right+8}px`,top:`${i.top+i.height/2-12}px`},g.value=!0}},h=()=>{g.value=!1};return ee(()=>{if(t.collapsed){const o=document.querySelector(`[data-menu-item="${t.item.key}"]`);o&&(o.addEventListener("mouseenter",r),o.addEventListener("mouseleave",h))}}),te(()=>{const o=document.querySelector(`[data-menu-item="${t.item.key}"]`);o&&(o.removeEventListener("mouseenter",r),o.removeEventListener("mouseleave",h))}),(o,i)=>{const f=Z("router-link");return c(),m("div",$e,[e("div",{class:I(w.value),role:o.item.hasChildren?"button":"link",tabindex:o.item.hasChildren?"0":void 0,"aria-expanded":o.item.hasChildren?o.item.expanded:void 0,onClick:A,onKeydown:[U(A,["enter"]),U(se(A,["prevent"]),["space"])]},[e("div",je,[(c(),m("svg",ze,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:o.item.icon},null,8,Ne)])),o.collapsed?$("",!0):(c(),m("span",Ve,v(o.item.label),1))]),o.item.hasChildren&&!o.collapsed?(c(),m("svg",{key:0,class:I(s.value),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},i[0]||(i[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2)):$("",!0)],42,Ie),o.item.hasChildren&&o.item.expanded&&!o.collapsed?(c(),m("div",Le,[(c(!0),m(P,null,E(o.item.children,S=>(c(),W(f,{key:S.key,to:S.route,class:I(M(S))},{default:_(()=>[C(v(S.label),1)]),_:2},1032,["to","class"]))),128))])):$("",!0),o.collapsed&&g.value?(c(),m("div",{key:1,class:"fixed z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg pointer-events-none",style:me(k.value)},v(o.item.label),5)):$("",!0)])}}});const B=O(Te,[["__scopeId","data-v-eab98f12"]]),Be={class:"flex-1 p-4 overflow-y-auto mobile-scroll"},Re={class:"space-y-2"},Oe=L({__name:"AdminNavigation",setup(l){const t=T(),a=D(),d=b(()=>({key:"dashboard",label:"Dashboard",icon:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z",route:"/admin-spa/dashboard",active:a.path==="/admin-spa/dashboard"||a.path==="/admin-spa"})),u=b(()=>({key:"auctions",label:"Auctions",icon:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",hasChildren:!0,expanded:t.openMenus.auctions,children:[{key:"auctions-all",label:"All Auctions",route:"/admin-spa/auctions"},{key:"auctions-create",label:"Create Auction",route:"/admin-spa/auctions/create"},{key:"auctions-live",label:"Live Auctions",route:"/admin-spa/auctions/live"},{key:"auctions-ended",label:"Ended Auctions",route:"/admin-spa/auctions/ended"},{key:"auctions-templates",label:"Auction Templates",route:"/admin-spa/auctions/templates"}]})),g=b(()=>({key:"items",label:"Items",icon:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4",hasChildren:!0,expanded:t.openMenus.items,children:[{key:"items-all",label:"All Items",route:"/admin-spa/items"},{key:"items-add",label:"Add Item",route:"/admin-spa/items/create"},{key:"items-categories",label:"Categories",route:"/admin-spa/items/categories"},{key:"items-bulk",label:"Bulk Import",route:"/admin-spa/items/bulk-import"},{key:"items-conditions",label:"Item Conditions",route:"/admin-spa/items/conditions"}]})),k=b(()=>({key:"users",label:"Users",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z",hasChildren:!0,expanded:t.openMenus.users,children:[{key:"users-all",label:"All Users",route:"/admin-spa/users"},{key:"users-bidders",label:"Bidders",route:"/admin-spa/users/bidders"},{key:"users-sellers",label:"Sellers",route:"/admin-spa/users/sellers"},{key:"users-admins",label:"Administrators",route:"/admin-spa/users/administrators"},{key:"users-roles",label:"User Roles",route:"/admin-spa/users/roles"},{key:"users-permissions",label:"Permissions",route:"/admin-spa/users/permissions"}]})),w=b(()=>({key:"financial",label:"Financial",icon:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1",hasChildren:!0,expanded:t.openMenus.financial,children:[{key:"financial-transactions",label:"Transactions",route:"/admin-spa/financial/transactions"},{key:"financial-payments",label:"Payments",route:"/admin-spa/financial/payments"},{key:"financial-commissions",label:"Commissions",route:"/admin-spa/financial/commissions"},{key:"financial-invoices",label:"Invoices",route:"/admin-spa/financial/invoices"},{key:"financial-tax",label:"Tax Reports",route:"/admin-spa/financial/tax-reports"}]})),s=b(()=>({key:"reports",label:"Reports",icon:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",hasChildren:!0,expanded:t.openMenus.reports,children:[{key:"reports-sales",label:"Sales Reports",route:"/admin-spa/reports/sales"},{key:"reports-analytics",label:"User Analytics",route:"/admin-spa/reports/analytics"},{key:"reports-performance",label:"Performance",route:"/admin-spa/reports/performance"},{key:"reports-custom",label:"Custom Reports",route:"/admin-spa/reports/custom"}]})),y=b(()=>({key:"settings",label:"Settings",icon:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z",hasChildren:!0,expanded:t.openMenus.settings,children:[{key:"settings-general",label:"General",route:"/admin-spa/settings/general"},{key:"settings-auctions",label:"Auction Settings",route:"/admin-spa/settings/auctions"},{key:"settings-payments",label:"Payment Gateway",route:"/admin-spa/settings/payments"},{key:"settings-email",label:"Email Templates",route:"/admin-spa/settings/email"},{key:"settings-logs",label:"System Logs",route:"/admin-spa/settings/logs"}]}));return(M,A)=>(c(),m("nav",Be,[e("div",Re,[p(B,{item:d.value,collapsed:n(t).sidebarCollapsed},null,8,["item","collapsed"]),p(B,{item:u.value,collapsed:n(t).sidebarCollapsed},null,8,["item","collapsed"]),p(B,{item:g.value,collapsed:n(t).sidebarCollapsed},null,8,["item","collapsed"]),p(B,{item:k.value,collapsed:n(t).sidebarCollapsed},null,8,["item","collapsed"]),p(B,{item:w.value,collapsed:n(t).sidebarCollapsed},null,8,["item","collapsed"]),p(B,{item:s.value,collapsed:n(t).sidebarCollapsed},null,8,["item","collapsed"]),p(B,{item:y.value,collapsed:n(t).sidebarCollapsed},null,8,["item","collapsed"])])]))}});const ne=O(Oe,[["__scopeId","data-v-b70089d0"]]),He={class:"p-4 border-b border-gray-200"},Pe={class:"flex items-center justify-between"},Ee={key:0,class:"flex items-center"},Ue={key:1,class:"w-12 h-12 mx-auto"},De={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Fe=["d"],We={class:"p-4 border-t border-gray-200"},Ge={key:0,class:"flex items-center"},Ze={class:"h-10 w-10 bg-primary-500 rounded-full flex items-center justify-center mr-3"},qe={class:"text-white font-medium"},Je={class:"flex-1"},Ke={class:"text-sm font-medium text-gray-900"},Qe={class:"text-xs text-gray-500"},Ye={key:1,class:"flex justify-center"},Xe={class:"h-10 w-10 bg-primary-500 rounded-full flex items-center justify-center"},et={class:"text-white font-medium"},tt=L({__name:"AdminSidebar",setup(l){const t=T(),a=F(),d=b(()=>["admin-sidebar bg-white border-r border-gray-200 flex flex-col z-50 transition-all duration-300",t.sidebarCollapsed?"w-16":"w-72","md:relative md:translate-x-0",t.mobileMenuOpen?"mobile-open":"","md:static fixed top-0 left-0 h-full",t.mobileMenuOpen?"translate-x-0":"-translate-x-full md:translate-x-0"]),u=b(()=>{var s;return((s=a.user)==null?void 0:s.name)||"Admin User"}),g=b(()=>{var s,y,M;return((M=(y=(s=a.user)==null?void 0:s.roles)==null?void 0:y[0])==null?void 0:M.name)||"Administrator"}),k=b(()=>u.value.split(" ").map(y=>y[0]).join("").toUpperCase().slice(0,2)),w=async()=>{try{await a.logout(),window.location.href="/login"}catch(s){console.error("Logout failed:",s)}};return(s,y)=>(c(),m("div",{class:I(d.value)},[e("div",He,[e("div",Pe,[n(t).sidebarCollapsed?(c(),m("div",Ue,y[2]||(y[2]=[G('<svg viewBox="0 0 260 260" class="w-full h-full drop-shadow-sm" data-v-40fc0bf4><circle fill="#243b53" cx="130" cy="130" r="130" data-v-40fc0bf4></circle><path fill="#fff" d="M184.38,167.7h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z" data-v-40fc0bf4></path><path fill="#fff" d="M125.73,128.23v15.55h-13.22l2.79,3.7-2.79,4.66h13.22v15.56h-40.82v-15.54l.74-1.24,2.21-3.7-2.95-3.87v-10.93l2.51-4.2h38.31Z" data-v-40fc0bf4></path><path fill="#fff" d="M184.38,123.37h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z" data-v-40fc0bf4></path><path fill="#fff" d="M96.65,123.37v-20.91h-12.65v-18.55h44.35v18.55h-12.65v20.91h-19.04Z" data-v-40fc0bf4></path><polygon fill="#fff" points="227.59 173.63 224 172.2 223.61 169.86 221.71 171.29 218.13 169.86 224.52 164.98 226.31 165.69 227.59 173.63" data-v-40fc0bf4></polygon><polygon fill="#fff" points="31.03 173.63 34.62 172.2 35 169.86 36.9 171.29 40.48 169.86 34.1 164.98 32.3 165.69 31.03 173.63" data-v-40fc0bf4></polygon></svg>',1)]))):(c(),m("div",Ee,y[1]||(y[1]=[G('<div class="w-12 h-12 mr-4" data-v-40fc0bf4><svg viewBox="0 0 260 260" class="w-full h-full drop-shadow-sm" data-v-40fc0bf4><circle fill="#243b53" cx="130" cy="130" r="130" data-v-40fc0bf4></circle><path fill="#fff" d="M184.38,167.7h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z" data-v-40fc0bf4></path><path fill="#fff" d="M125.73,128.23v15.55h-13.22l2.79,3.7-2.79,4.66h13.22v15.56h-40.82v-15.54l.74-1.24,2.21-3.7-2.95-3.87v-10.93l2.51-4.2h38.31Z" data-v-40fc0bf4></path><path fill="#fff" d="M184.38,123.37h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z" data-v-40fc0bf4></path><path fill="#fff" d="M96.65,123.37v-20.91h-12.65v-18.55h44.35v18.55h-12.65v20.91h-19.04Z" data-v-40fc0bf4></path><polygon fill="#fff" points="227.59 173.63 224 172.2 223.61 169.86 221.71 171.29 218.13 169.86 224.52 164.98 226.31 165.69 227.59 173.63" data-v-40fc0bf4></polygon><polygon fill="#fff" points="31.03 173.63 34.62 172.2 35 169.86 36.9 171.29 40.48 169.86 34.1 164.98 32.3 165.69 31.03 173.63" data-v-40fc0bf4></polygon></svg></div><div class="hidden sm:block" data-v-40fc0bf4><h1 class="text-xl font-bold text-gray-900" data-v-40fc0bf4>Vertigo AMS</h1><p class="text-sm text-gray-500" data-v-40fc0bf4>Admin Panel</p></div>',2)]))),e("button",{onClick:y[0]||(y[0]=(...M)=>n(t).toggleSidebar&&n(t).toggleSidebar(...M)),class:"hidden md:block p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"},[(c(),m("svg",De,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:n(t).sidebarCollapsed?"M9 5l7 7-7 7":"M15 19l-7-7 7-7"},null,8,Fe)]))])])]),p(ne),e("div",We,[n(t).sidebarCollapsed?(c(),m("div",Ye,[e("div",Xe,[e("span",et,v(k.value),1)])])):(c(),m("div",Ge,[e("div",Ze,[e("span",qe,v(k.value),1)]),e("div",Je,[e("p",Ke,v(u.value),1),e("p",Qe,v(g.value),1)]),e("button",{onClick:w,class:"p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200",title:"Logout"},y[3]||(y[3]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})],-1)]))]))])],2))}});const oe=O(tt,[["__scopeId","data-v-40fc0bf4"]]),st={class:"bg-white border-b border-gray-200 px-4 sm:px-6 py-4"},at={class:"flex items-center justify-between"},nt={class:"flex-1 md:flex-none"},ot={class:"text-lg sm:text-xl font-semibold text-gray-900"},it={key:0,class:"text-xs sm:text-sm text-gray-500 hidden sm:block"},lt={class:"flex items-center space-x-2 sm:space-x-4"},rt={class:"hidden sm:flex items-center space-x-2"},dt={class:"hidden lg:inline"},ct={class:"lg:hidden"},ut={key:0,class:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"},mt={class:"relative hidden lg:block"},pt={key:0,class:"mt-4 lg:hidden"},vt={class:"relative"},ft=L({__name:"AdminHeader",props:{quickActions:{default:()=>[]}},setup(l){const t=T(),a=J(),d=D(),u=z(""),g=z(!1),k=b(()=>{var h;return((h=d.meta)==null?void 0:h.title)||"Dashboard"}),w=b(()=>{var h;return((h=d.meta)==null?void 0:h.subtitle)||"Monitor your auction platform performance"}),s=b(()=>a.unreadCount),y=h=>{h.action()},M=()=>{t.toggleNotifications()},A=()=>{g.value=!g.value},r=()=>{u.value.trim()&&console.log("Searching for:",u.value)};return(h,o)=>(c(),m("header",st,[e("div",at,[e("button",{onClick:o[0]||(o[0]=(...i)=>n(t).toggleMobileMenu&&n(t).toggleMobileMenu(...i)),class:"md:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"},o[3]||(o[3]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",nt,[e("h2",ot,v(k.value),1),w.value?(c(),m("p",it,v(w.value),1)):$("",!0)]),e("div",lt,[e("div",rt,[(c(!0),m(P,null,E(h.quickActions,i=>(c(),W(n(j),{key:i.key,variant:i.variant||"primary",size:i.size||"sm",onClick:f=>y(i),class:"text-sm"},{default:_(()=>[i.icon?(c(),W(pe(i.icon),{key:0,class:"w-4 h-4 mr-1"})):$("",!0),e("span",dt,v(i.label),1),e("span",ct,v(i.shortLabel||i.label),1)]),_:2},1032,["variant","size","onClick"]))),128))]),e("button",{onClick:M,class:"relative p-2 text-gray-400 hover:text-gray-500 rounded-lg hover:bg-gray-100 transition-colors duration-200"},[o[4]||(o[4]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18 8A6 6 0 006 8c0 7-3 9-3 9h18s-3-2-3-9z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.73 21a2 2 0 01-3.46 0"})],-1)),s.value>0?(c(),m("span",ut,v(s.value>9?"9+":s.value),1)):$("",!0)]),e("div",mt,[Q(e("input",{"onUpdate:modelValue":o[1]||(o[1]=i=>u.value=i),type:"text",placeholder:"Search...",class:"w-48 xl:w-64 pl-8 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",onKeyup:U(r,["enter"])},null,544),[[Y,u.value]]),o[5]||(o[5]=e("svg",{class:"absolute left-2 top-2.5 h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))]),e("button",{onClick:A,class:"lg:hidden p-2 text-gray-400 hover:text-gray-500 rounded-lg hover:bg-gray-100 transition-colors duration-200"},o[6]||(o[6]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)]))])]),g.value?(c(),m("div",pt,[e("div",vt,[Q(e("input",{"onUpdate:modelValue":o[2]||(o[2]=i=>u.value=i),type:"text",placeholder:"Search...",class:"w-full pl-8 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",onKeyup:U(r,["enter"])},null,544),[[Y,u.value]]),o[7]||(o[7]=e("svg",{class:"absolute left-2 top-2.5 h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])])):$("",!0)]))}});const ie=O(ft,[["__scopeId","data-v-d6137539"]]),gt=L({__name:"AdminContainer",props:{fluid:{type:Boolean,default:!1},padding:{default:"md"},maxWidth:{default:"none"}},setup(l){const t=l,a=b(()=>{const d=["admin-container"],u={none:"",sm:"p-2 sm:p-4",md:"p-4 sm:p-6",lg:"p-6 sm:p-8",xl:"p-8 sm:p-12"};if(u[t.padding]&&d.push(u[t.padding]),!t.fluid&&t.maxWidth!=="none"){const g={sm:"max-w-sm mx-auto",md:"max-w-md mx-auto",lg:"max-w-lg mx-auto",xl:"max-w-xl mx-auto","2xl":"max-w-2xl mx-auto","3xl":"max-w-3xl mx-auto","4xl":"max-w-4xl mx-auto","5xl":"max-w-5xl mx-auto","6xl":"max-w-6xl mx-auto","7xl":"max-w-7xl mx-auto",full:"max-w-full"};g[t.maxWidth]&&d.push(g[t.maxWidth])}return d.join(" ")});return(d,u)=>(c(),m("div",{class:I(a.value)},[ve(d.$slots,"default",{},void 0,!0)],2))}});const le=O(gt,[["__scopeId","data-v-2cb42a36"]]),ht={class:"mobile-menu-item"},bt=["role","tabindex","aria-expanded","onKeydown"],yt={class:"flex items-center flex-1"},xt={class:"h-6 w-6 mr-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},kt=["d"],_t={class:"text-base font-medium"},wt={key:0,class:"ml-10 mt-2 space-y-1"},Mt=["onClick"],Ct=L({__name:"MobileMenuItem",props:{item:{}},emits:["navigate"],setup(l,{emit:t}){const a=l,d=t,u=T(),g=D(),k=b(()=>{const r=["w-full flex items-center justify-between px-4 py-4 rounded-lg transition-all duration-200 mobile-touch-target"];return a.item.active||s.value?r.push("bg-white text-primary-700 border border-primary-200 shadow-sm"):r.push("text-gray-700 hover:text-gray-900 hover:bg-gray-50 active:bg-gray-100"),r.join(" ")}),w=b(()=>["h-5 w-5 transition-transform duration-200 flex-shrink-0",a.item.expanded?"rotate-180":""]),s=b(()=>a.item.route?g.path===a.item.route:a.item.children?a.item.children.some(r=>g.path===r.route):!1),y=r=>{const h=["w-full text-left block px-4 py-3 text-sm rounded-md mobile-touch-target transition-colors duration-200"];return g.path===r.route?h.push("text-primary-700 bg-white font-medium border border-primary-200 shadow-sm"):h.push("text-gray-600 hover:text-gray-900 hover:bg-gray-50 active:bg-gray-100"),h.join(" ")},M=()=>{a.item.hasChildren?u.toggleMenu(a.item.key):a.item.route&&d("navigate",a.item.route)},A=r=>{d("navigate",r)};return(r,h)=>(c(),m("div",ht,[e("div",{class:I(k.value),role:r.item.hasChildren?"button":void 0,tabindex:r.item.hasChildren?"0":void 0,"aria-expanded":r.item.hasChildren?r.item.expanded:void 0,onClick:M,onKeydown:[U(M,["enter"]),U(se(M,["prevent"]),["space"])]},[e("div",yt,[(c(),m("svg",xt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:r.item.icon},null,8,kt)])),e("span",_t,v(r.item.label),1)]),r.item.hasChildren?(c(),m("svg",{key:0,class:I(w.value),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},h[0]||(h[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2)):$("",!0)],42,bt),r.item.hasChildren&&r.item.expanded?(c(),m("div",wt,[(c(!0),m(P,null,E(r.item.children,o=>(c(),m("button",{key:o.key,class:I(y(o)),onClick:i=>A(o.route)},v(o.label),11,Mt))),128))])):$("",!0)]))}});const R=O(Ct,[["__scopeId","data-v-5bf88a00"]]),At={key:0,class:"fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-xl lg:hidden"},St={class:"flex items-center justify-between p-4 border-b border-gray-200"},$t={class:"flex-1 overflow-y-auto mobile-scroll"},It={class:"p-4"},jt={class:"space-y-2"},zt={class:"border-t border-gray-200 p-4"},Nt={class:"flex items-center space-x-3"},Vt={class:"flex-1 min-w-0"},Lt={class:"text-sm font-medium text-gray-900 truncate"},Tt={class:"text-sm text-gray-500 truncate"},Bt=L({__name:"AdminMobileMenu",setup(l){const t=T(),a=F(),d=D(),u=q(),g=b(()=>({key:"dashboard",label:"Dashboard",icon:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z",route:"/admin-spa/dashboard",active:d.path==="/admin-spa/dashboard"||d.path==="/admin-spa"})),k=b(()=>({key:"auctions",label:"Auctions",icon:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",hasChildren:!0,expanded:t.openMenus.auctions,children:[{key:"auctions-all",label:"All Auctions",route:"/admin-spa/auctions"},{key:"auctions-create",label:"Create Auction",route:"/admin-spa/auctions/create"},{key:"auctions-live",label:"Live Auctions",route:"/admin-spa/auctions/live"},{key:"auctions-ended",label:"Ended Auctions",route:"/admin-spa/auctions/ended"},{key:"auctions-templates",label:"Auction Templates",route:"/admin-spa/auctions/templates"}]})),w=b(()=>({key:"items",label:"Items",icon:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4",hasChildren:!0,expanded:t.openMenus.items,children:[{key:"items-all",label:"All Items",route:"/admin-spa/items"},{key:"items-add",label:"Add Item",route:"/admin-spa/items/create"},{key:"items-categories",label:"Categories",route:"/admin-spa/items/categories"},{key:"items-bulk",label:"Bulk Import",route:"/admin-spa/items/bulk-import"},{key:"items-conditions",label:"Item Conditions",route:"/admin-spa/items/conditions"}]})),s=b(()=>({key:"users",label:"Users",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z",hasChildren:!0,expanded:t.openMenus.users,children:[{key:"users-all",label:"All Users",route:"/admin-spa/users"},{key:"users-bidders",label:"Bidders",route:"/admin-spa/users/bidders"},{key:"users-sellers",label:"Sellers",route:"/admin-spa/users/sellers"},{key:"users-admins",label:"Administrators",route:"/admin-spa/users/administrators"},{key:"users-roles",label:"User Roles",route:"/admin-spa/users/roles"},{key:"users-permissions",label:"Permissions",route:"/admin-spa/users/permissions"}]})),y=b(()=>({key:"financial",label:"Financial",icon:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1",hasChildren:!0,expanded:t.openMenus.financial,children:[{key:"financial-transactions",label:"Transactions",route:"/admin-spa/financial/transactions"},{key:"financial-payments",label:"Payments",route:"/admin-spa/financial/payments"},{key:"financial-commissions",label:"Commissions",route:"/admin-spa/financial/commissions"},{key:"financial-invoices",label:"Invoices",route:"/admin-spa/financial/invoices"},{key:"financial-tax",label:"Tax Reports",route:"/admin-spa/financial/tax-reports"}]})),M=b(()=>({key:"reports",label:"Reports",icon:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",hasChildren:!0,expanded:t.openMenus.reports,children:[{key:"reports-sales",label:"Sales Reports",route:"/admin-spa/reports/sales"},{key:"reports-analytics",label:"User Analytics",route:"/admin-spa/reports/analytics"},{key:"reports-performance",label:"Performance",route:"/admin-spa/reports/performance"},{key:"reports-custom",label:"Custom Reports",route:"/admin-spa/reports/custom"}]})),A=b(()=>({key:"settings",label:"Settings",icon:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z",hasChildren:!0,expanded:t.openMenus.settings,children:[{key:"settings-general",label:"General",route:"/admin-spa/settings/general"},{key:"settings-auctions",label:"Auction Settings",route:"/admin-spa/settings/auctions"},{key:"settings-payments",label:"Payment Gateway",route:"/admin-spa/settings/payments"},{key:"settings-email",label:"Email Templates",route:"/admin-spa/settings/email"},{key:"settings-logs",label:"System Logs",route:"/admin-spa/settings/logs"}]})),r=o=>{u.push(o),t.closeMobileMenu()},h=async()=>{try{await a.logout(),u.push("/login"),t.closeMobileMenu()}catch(o){console.error("Logout failed:",o)}};return(o,i)=>(c(),W(fe,{to:"body"},[p(X,{"enter-active-class":"transition-opacity duration-300","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"transition-opacity duration-300","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:_(()=>[n(t).mobileMenuOpen?(c(),m("div",{key:0,class:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:i[0]||(i[0]=(...f)=>n(t).closeMobileMenu&&n(t).closeMobileMenu(...f))})):$("",!0)]),_:1}),p(X,{"enter-active-class":"transition-transform duration-300","enter-from-class":"-translate-x-full","enter-to-class":"translate-x-0","leave-active-class":"transition-transform duration-300","leave-from-class":"translate-x-0","leave-to-class":"-translate-x-full"},{default:_(()=>{var f,S;return[n(t).mobileMenuOpen?(c(),m("div",At,[e("div",St,[i[3]||(i[3]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"flex-shrink-0"},[e("img",{src:"/images/logo.png",alt:"Vertigo AMS",class:"h-8 w-auto"})]),e("div",null,[e("h2",{class:"text-lg font-semibold text-gray-900"},"Admin Panel"),e("p",{class:"text-sm text-gray-500"},"Vertigo AMS")])],-1)),e("button",{onClick:i[1]||(i[1]=(...N)=>n(t).closeMobileMenu&&n(t).closeMobileMenu(...N)),class:"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"},i[2]||(i[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",$t,[e("nav",It,[e("div",jt,[p(R,{item:g.value,onNavigate:r},null,8,["item"]),p(R,{item:k.value,onNavigate:r},null,8,["item"]),p(R,{item:w.value,onNavigate:r},null,8,["item"]),p(R,{item:s.value,onNavigate:r},null,8,["item"]),p(R,{item:y.value,onNavigate:r},null,8,["item"]),p(R,{item:M.value,onNavigate:r},null,8,["item"]),p(R,{item:A.value,onNavigate:r},null,8,["item"])])])]),e("div",zt,[e("div",Nt,[i[5]||(i[5]=e("div",{class:"flex-shrink-0"},[e("div",{class:"h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])])],-1)),e("div",Vt,[e("p",Lt,v(((f=n(a).user)==null?void 0:f.name)||"Admin User"),1),e("p",Tt,v(((S=n(a).user)==null?void 0:S.email)||"<EMAIL>"),1)]),e("button",{onClick:h,class:"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200",title:"Logout"},i[4]||(i[4]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013 3v1"})],-1)]))])])])):$("",!0)]}),_:1})]))}});const re=O(Bt,[["__scopeId","data-v-ca5b4059"]]),Rt={class:"min-h-screen bg-gray-100"},Ot={class:"flex h-screen overflow-hidden"},Ht={class:"flex-1 flex flex-col overflow-hidden"},Pt={class:"flex-1 overflow-y-auto bg-gray-50"},Et=L({__name:"AdminLayout",setup(l){const t=T(),a=()=>{window.innerWidth>=768&&t.closeMobileMenu()};return ee(()=>{window.addEventListener("resize",a),t.initialize()}),te(()=>{window.removeEventListener("resize",a)}),(d,u)=>{const g=Z("router-view");return c(),m("div",Rt,[n(t).mobileMenuOpen?(c(),m("div",{key:0,onClick:u[0]||(u[0]=(...k)=>n(t).closeMobileMenu&&n(t).closeMobileMenu(...k)),class:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"})):$("",!0),e("div",Ot,[p(oe),e("div",Ht,[p(ie),e("main",Pt,[p(le,null,{default:_(()=>[p(g)]),_:1})])])]),p(re)])}}});const de=O(Et,[["__scopeId","data-v-a6c010a9"]]);function Ut(){const l=T(),t=D(),a=q(),d=z(""),u=z([]),g=b(()=>[{key:"dashboard",label:"Dashboard",icon:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z",route:"/admin-spa/dashboard",active:t.path==="/admin-spa/dashboard"||t.path==="/admin-spa"},{key:"auctions",label:"Auctions",icon:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",hasChildren:!0,expanded:l.openMenus.auctions,children:[{key:"auctions-all",label:"All Auctions",route:"/admin-spa/auctions"},{key:"auctions-create",label:"Create Auction",route:"/admin-spa/auctions/create"},{key:"auctions-live",label:"Live Auctions",route:"/admin-spa/auctions/live",badge:"3"},{key:"auctions-ended",label:"Ended Auctions",route:"/admin-spa/auctions/ended"},{key:"auctions-templates",label:"Auction Templates",route:"/admin-spa/auctions/templates"}]},{key:"items",label:"Items",icon:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4",hasChildren:!0,expanded:l.openMenus.items,children:[{key:"items-all",label:"All Items",route:"/admin-spa/items"},{key:"items-add",label:"Add Item",route:"/admin-spa/items/create"},{key:"items-categories",label:"Categories",route:"/admin-spa/items/categories"},{key:"items-bulk",label:"Bulk Import",route:"/admin-spa/items/bulk-import"},{key:"items-conditions",label:"Item Conditions",route:"/admin-spa/items/conditions"}]},{key:"users",label:"Users",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z",hasChildren:!0,expanded:l.openMenus.users,children:[{key:"users-all",label:"All Users",route:"/admin-spa/users"},{key:"users-bidders",label:"Bidders",route:"/admin-spa/users/bidders"},{key:"users-sellers",label:"Sellers",route:"/admin-spa/users/sellers"},{key:"users-admins",label:"Administrators",route:"/admin-spa/users/administrators"},{key:"users-roles",label:"User Roles",route:"/admin-spa/users/roles"},{key:"users-permissions",label:"Permissions",route:"/admin-spa/users/permissions"}]},{key:"financial",label:"Financial",icon:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1",hasChildren:!0,expanded:l.openMenus.financial,children:[{key:"financial-transactions",label:"Transactions",route:"/admin-spa/financial/transactions"},{key:"financial-payments",label:"Payments",route:"/admin-spa/financial/payments",badge:"12"},{key:"financial-commissions",label:"Commissions",route:"/admin-spa/financial/commissions"},{key:"financial-invoices",label:"Invoices",route:"/admin-spa/financial/invoices"},{key:"financial-tax",label:"Tax Reports",route:"/admin-spa/financial/tax-reports"}]},{key:"reports",label:"Reports",icon:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",hasChildren:!0,expanded:l.openMenus.reports,children:[{key:"reports-sales",label:"Sales Reports",route:"/admin-spa/reports/sales"},{key:"reports-analytics",label:"User Analytics",route:"/admin-spa/reports/analytics"},{key:"reports-performance",label:"Performance",route:"/admin-spa/reports/performance"},{key:"reports-custom",label:"Custom Reports",route:"/admin-spa/reports/custom"}]},{key:"settings",label:"Settings",icon:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z",hasChildren:!0,expanded:l.openMenus.settings,children:[{key:"settings-general",label:"General",route:"/admin-spa/settings/general"},{key:"settings-auctions",label:"Auction Settings",route:"/admin-spa/settings/auctions"},{key:"settings-payments",label:"Payment Gateway",route:"/admin-spa/settings/payments"},{key:"settings-email",label:"Email Templates",route:"/admin-spa/settings/email"},{key:"settings-logs",label:"System Logs",route:"/admin-spa/settings/logs"}]}]),k=b(()=>s(t.path)),w=b(()=>{const i=k.value;if(!i)return[];const f=[];return i.parent&&f.push({label:i.parent.label,route:i.parent.route}),f.push({label:i.label,route:i.route,current:!0}),f}),s=i=>{for(const f of g.value){if(f.route===i)return f;if(f.children){for(const S of f.children)if(S.route===i)return{...S,parent:f}}}return null};return{navigationItems:g,currentItem:k,breadcrumbs:w,searchQuery:d,filteredItems:u,findItemByRoute:s,navigateTo:i=>{a.push(i)},toggleMenu:i=>{l.toggleMenu(i)},searchNavigation:i=>{if(d.value=i,!i.trim()){u.value=[];return}const f=[],S=i.toLowerCase();g.value.forEach(N=>{N.label.toLowerCase().includes(S)&&f.push(N),N.children&&N.children.forEach(V=>{V.label.toLowerCase().includes(S)&&f.push({...V,parent:N})})}),u.value=f},clearSearch:()=>{d.value="",u.value=[]},isActiveRoute:i=>t.path===i,isParentActive:i=>i.children?i.children.some(f=>t.path===f.route):!1}}const Dt={class:"space-y-6"},Ft={class:"flex items-center justify-between"},Wt={class:"flex space-x-3"},Gt={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"},Zt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},qt=L({meta:{title:"Dashboard Overview",subtitle:"Monitor your auction platform performance"},__name:"AdminDashboard",setup(l){return(t,a)=>(c(),m("div",Dt,[e("div",Ft,[a[2]||(a[2]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Dashboard Overview"),e("p",{class:"text-gray-600"},"Monitor your auction platform performance")],-1)),e("div",Wt,[p(n(j),{variant:"outline",size:"sm"},{default:_(()=>a[0]||(a[0]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"})],-1),C(" Export ")])),_:1,__:[0]}),p(n(j),{variant:"primary",size:"sm"},{default:_(()=>a[1]||(a[1]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),C(" Create Auction ")])),_:1,__:[1]})])]),e("div",Gt,[p(n(H),{class:"p-6"},{default:_(()=>a[3]||(a[3]=[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])])]),e("div",{class:"ml-4"},[e("p",{class:"text-sm font-medium text-gray-500"},"Active Auctions"),e("p",{class:"text-2xl font-bold text-gray-900"},"24"),e("p",{class:"text-sm text-green-600"},"↗ +12% from last month")])],-1)])),_:1,__:[3]}),p(n(H),{class:"p-6"},{default:_(()=>a[4]||(a[4]=[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])]),e("div",{class:"ml-4"},[e("p",{class:"text-sm font-medium text-gray-500"},"Total Revenue"),e("p",{class:"text-2xl font-bold text-gray-900"},"$45,231"),e("p",{class:"text-sm text-green-600"},"↗ +8% from last month")])],-1)])),_:1,__:[4]}),p(n(H),{class:"p-6"},{default:_(()=>a[5]||(a[5]=[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})])])]),e("div",{class:"ml-4"},[e("p",{class:"text-sm font-medium text-gray-500"},"Registered Users"),e("p",{class:"text-2xl font-bold text-gray-900"},"1,429"),e("p",{class:"text-sm text-green-600"},"↗ +23% from last month")])],-1)])),_:1,__:[5]}),p(n(H),{class:"p-6"},{default:_(()=>a[6]||(a[6]=[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})])])]),e("div",{class:"ml-4"},[e("p",{class:"text-sm font-medium text-gray-500"},"Items Listed"),e("p",{class:"text-2xl font-bold text-gray-900"},"156"),e("p",{class:"text-sm text-red-600"},"↘ -3% from last month")])],-1)])),_:1,__:[6]})]),e("div",Zt,[p(n(H),{class:"p-6"},{default:_(()=>a[7]||(a[7]=[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Recent Auctions",-1),e("div",{class:"space-y-3"},[e("div",{class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[e("div",null,[e("p",{class:"font-medium text-gray-900"},"Vintage Watch Collection"),e("p",{class:"text-sm text-gray-500"},"Ends in 2 hours")]),e("span",{class:"text-green-600 font-semibold"},"$1,250")]),e("div",{class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[e("div",null,[e("p",{class:"font-medium text-gray-900"},"Art Deco Furniture"),e("p",{class:"text-sm text-gray-500"},"Ends in 1 day")]),e("span",{class:"text-green-600 font-semibold"},"$850")]),e("div",{class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[e("div",null,[e("p",{class:"font-medium text-gray-900"},"Classic Car Parts"),e("p",{class:"text-sm text-gray-500"},"Ends in 3 days")]),e("span",{class:"text-green-600 font-semibold"},"$2,100")])],-1)])),_:1,__:[7]}),p(n(H),{class:"p-6"},{default:_(()=>a[8]||(a[8]=[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"System Status",-1),e("div",{class:"space-y-3"},[e("div",{class:"flex items-center justify-between"},[e("span",{class:"text-gray-600"},"Server Status"),e("span",{class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"}," Online ")]),e("div",{class:"flex items-center justify-between"},[e("span",{class:"text-gray-600"},"Database"),e("span",{class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"}," Connected ")]),e("div",{class:"flex items-center justify-between"},[e("span",{class:"text-gray-600"},"Payment Gateway"),e("span",{class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"}," Active ")]),e("div",{class:"flex items-center justify-between"},[e("span",{class:"text-gray-600"},"Email Service"),e("span",{class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"}," Warning ")])],-1)])),_:1,__:[8]})])]))}}),Jt={class:"space-y-8"},Kt={class:"bg-white rounded-lg shadow p-6"},Qt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Yt={class:"space-y-2 text-sm"},Xt={class:"flex justify-between"},es={class:"flex justify-between"},ts={class:"flex justify-between"},ss={class:"flex justify-between"},as={class:"space-y-2 text-sm"},ns={class:"capitalize"},os={class:"bg-white rounded-lg shadow p-6"},is={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},ls={class:"bg-white rounded-lg shadow p-6"},rs={class:"space-y-2"},ds={class:"bg-white rounded-lg shadow p-6"},cs={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},us={key:0,class:"space-y-2 text-sm"},ms={key:0},ps={key:1,class:"text-gray-500"},vs={class:"space-y-1"},fs={key:0,class:"font-medium text-primary-600"},gs={key:1,class:"text-gray-500"},hs={class:"mt-6"},bs={class:"flex space-x-4"},ys={key:0,class:"mt-4"},xs={class:"space-y-1"},ks=["onClick"],_s={key:0,class:"text-gray-500"},ws={class:"bg-white rounded-lg shadow p-6"},Ms={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},Cs=L({meta:{title:"Component Test",subtitle:"Testing admin layout components"},__name:"AdminTest",setup(l){const t=T(),a=Ut(),d=z(""),u=()=>{t.toggleMenu("auctions")},g=()=>{a.searchNavigation(d.value)},k=()=>{d.value="",a.clearSearch()};return(w,s)=>{var M,A;const y=Z("router-link");return c(),m("div",Jt,[s[31]||(s[31]=G('<div class="bg-white rounded-lg shadow p-6"><h1 class="text-2xl font-bold text-gray-900 mb-4">Admin Navigation Component Test</h1><p class="text-gray-600 mb-6"> This page tests all the admin layout and navigation components we&#39;ve created in Phase 1 &amp; 2. </p><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"><div class="flex items-center space-x-3"><div class="w-3 h-3 bg-green-500 rounded-full"></div><span class="text-sm font-medium">AdminLayout.vue</span></div><div class="flex items-center space-x-3"><div class="w-3 h-3 bg-green-500 rounded-full"></div><span class="text-sm font-medium">AdminSidebar.vue</span></div><div class="flex items-center space-x-3"><div class="w-3 h-3 bg-green-500 rounded-full"></div><span class="text-sm font-medium">AdminHeader.vue</span></div><div class="flex items-center space-x-3"><div class="w-3 h-3 bg-green-500 rounded-full"></div><span class="text-sm font-medium">AdminContainer.vue</span></div><div class="flex items-center space-x-3"><div class="w-3 h-3 bg-green-500 rounded-full"></div><span class="text-sm font-medium">AdminNavigation.vue</span></div><div class="flex items-center space-x-3"><div class="w-3 h-3 bg-green-500 rounded-full"></div><span class="text-sm font-medium">AdminMenuItem.vue</span></div><div class="flex items-center space-x-3"><div class="w-3 h-3 bg-green-500 rounded-full"></div><span class="text-sm font-medium">AdminMobileMenu.vue</span></div><div class="flex items-center space-x-3"><div class="w-3 h-3 bg-green-500 rounded-full"></div><span class="text-sm font-medium">MobileMenuItem.vue</span></div><div class="flex items-center space-x-3"><div class="w-3 h-3 bg-green-500 rounded-full"></div><span class="text-sm font-medium">useAdminNavigation.ts</span></div></div></div>',1)),e("div",Kt,[s[7]||(s[7]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Admin Store State",-1)),e("div",Qt,[e("div",null,[s[5]||(s[5]=e("h3",{class:"text-lg font-medium text-gray-900 mb-3"},"Current State",-1)),e("div",Yt,[e("div",Xt,[s[1]||(s[1]=e("span",null,"Sidebar Collapsed:",-1)),e("span",{class:I(n(t).sidebarCollapsed?"text-green-600":"text-red-600")},v(n(t).sidebarCollapsed?"Yes":"No"),3)]),e("div",es,[s[2]||(s[2]=e("span",null,"Mobile Menu Open:",-1)),e("span",{class:I(n(t).mobileMenuOpen?"text-green-600":"text-red-600")},v(n(t).mobileMenuOpen?"Yes":"No"),3)]),e("div",ts,[s[3]||(s[3]=e("span",null,"Notifications Open:",-1)),e("span",{class:I(n(t).notificationsOpen?"text-green-600":"text-red-600")},v(n(t).notificationsOpen?"Yes":"No"),3)]),e("div",ss,[s[4]||(s[4]=e("span",null,"Store Initialized:",-1)),e("span",{class:I(n(t).isInitialized?"text-green-600":"text-red-600")},v(n(t).isInitialized?"Yes":"No"),3)])])]),e("div",null,[s[6]||(s[6]=e("h3",{class:"text-lg font-medium text-gray-900 mb-3"},"Open Menus",-1)),e("div",as,[(c(!0),m(P,null,E(n(t).openMenus,(r,h)=>(c(),m("div",{key:h,class:"flex justify-between"},[e("span",ns,v(h)+":",1),e("span",{class:I(r?"text-green-600":"text-gray-400")},v(r?"Open":"Closed"),3)]))),128))])])])]),e("div",os,[s[11]||(s[11]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Interactive Controls",-1)),e("div",is,[p(n(j),{onClick:n(t).toggleSidebar,variant:"outline",class:"w-full"},{default:_(()=>[C(v(n(t).sidebarCollapsed?"Expand":"Collapse")+" Sidebar ",1)]),_:1},8,["onClick"]),p(n(j),{onClick:n(t).toggleMobileMenu,variant:"outline",class:"w-full"},{default:_(()=>[C(v(n(t).mobileMenuOpen?"Close":"Open")+" Mobile Menu ",1)]),_:1},8,["onClick"]),p(n(j),{onClick:n(t).toggleNotifications,variant:"outline",class:"w-full"},{default:_(()=>[C(v(n(t).notificationsOpen?"Close":"Open")+" Notifications ",1)]),_:1},8,["onClick"]),p(n(j),{onClick:u,variant:"outline",class:"w-full"},{default:_(()=>s[8]||(s[8]=[C(" Toggle Auctions Menu ")])),_:1,__:[8]}),p(n(j),{onClick:n(t).closeAllMenus,variant:"outline",class:"w-full"},{default:_(()=>s[9]||(s[9]=[C(" Close All Menus ")])),_:1,__:[9]},8,["onClick"]),p(n(j),{onClick:n(t).reset,variant:"danger",class:"w-full"},{default:_(()=>s[10]||(s[10]=[C(" Reset Store ")])),_:1,__:[10]},8,["onClick"])])]),e("div",ls,[s[14]||(s[14]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Navigation Test",-1)),s[15]||(s[15]=e("p",{class:"text-gray-600 mb-4"}," Test the navigation by clicking on menu items in the sidebar. The active states should update correctly. ",-1)),e("div",rs,[p(y,{to:"/admin-spa/dashboard",class:"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"},{default:_(()=>s[12]||(s[12]=[C(" Go to Dashboard ")])),_:1,__:[12]}),p(y,{to:"/admin-spa/test",class:"inline-block px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors ml-2"},{default:_(()=>s[13]||(s[13]=[C(" Stay on Test Page ")])),_:1,__:[13]})])]),e("div",ds,[s[25]||(s[25]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Navigation Composable Test",-1)),e("div",cs,[e("div",null,[s[20]||(s[20]=e("h3",{class:"text-lg font-medium text-gray-900 mb-3"},"Current Navigation Item",-1)),n(a).currentItem.value?(c(),m("div",us,[e("div",null,[s[16]||(s[16]=e("strong",null,"Label:",-1)),C(" "+v(n(a).currentItem.value.label),1)]),e("div",null,[s[17]||(s[17]=e("strong",null,"Key:",-1)),C(" "+v(n(a).currentItem.value.key),1)]),e("div",null,[s[18]||(s[18]=e("strong",null,"Route:",-1)),C(" "+v(n(a).currentItem.value.route),1)]),n(a).currentItem.value.parent?(c(),m("div",ms,[s[19]||(s[19]=e("strong",null,"Parent:",-1)),C(" "+v(n(a).currentItem.value.parent.label),1)])):$("",!0)])):(c(),m("div",ps,"No current item found"))]),e("div",null,[s[21]||(s[21]=e("h3",{class:"text-lg font-medium text-gray-900 mb-3"},"Breadcrumbs",-1)),e("div",vs,[(c(!0),m(P,null,E(n(a).breadcrumbs.value,(r,h)=>(c(),m("div",{key:h,class:"text-sm"},[r.current?(c(),m("span",fs,v(r.label),1)):(c(),m("span",gs,v(r.label)+" /",1))]))),128))])])]),e("div",hs,[s[24]||(s[24]=e("h3",{class:"text-lg font-medium text-gray-900 mb-3"},"Navigation Search Test",-1)),e("div",bs,[p(n(ae),{modelValue:d.value,"onUpdate:modelValue":s[0]||(s[0]=r=>d.value=r),placeholder:"Search navigation...",onInput:g,class:"flex-1"},null,8,["modelValue"]),p(n(j),{onClick:k,variant:"outline"},{default:_(()=>s[22]||(s[22]=[C("Clear")])),_:1,__:[22]})]),n(a).filteredItems.value.length>0?(c(),m("div",ys,[s[23]||(s[23]=e("h4",{class:"font-medium text-gray-900 mb-2"},"Search Results:",-1)),e("div",xs,[(c(!0),m(P,null,E(n(a).filteredItems.value,r=>(c(),m("div",{key:r.key,class:"text-sm"},[e("button",{onClick:h=>n(a).navigateTo(r.route||""),class:"text-blue-600 hover:text-blue-800"},[C(v(r.label)+" ",1),r.parent?(c(),m("span",_s," (in "+v(r.parent.label)+") ",1)):$("",!0)],8,ks)]))),128))])])):$("",!0)])]),s[32]||(s[32]=G('<div class="bg-white rounded-lg shadow p-6"><h2 class="text-xl font-semibold text-gray-900 mb-4">Responsive Design Test</h2><div class="space-y-4"><div class="p-4 bg-gray-50 rounded-lg"><h3 class="font-medium text-gray-900 mb-2">Desktop (≥768px)</h3><ul class="text-sm text-gray-600 space-y-1"><li>• Sidebar should be visible and collapsible</li><li>• Header should show full search bar</li><li>• Mobile menu button should be hidden</li></ul></div><div class="p-4 bg-gray-50 rounded-lg"><h3 class="font-medium text-gray-900 mb-2">Mobile (&lt;768px)</h3><ul class="text-sm text-gray-600 space-y-1"><li>• Sidebar should be hidden by default</li><li>• Mobile menu button should be visible in header</li><li>• Search should be collapsible</li><li>• Touch targets should be 44px minimum</li></ul></div></div></div>',1)),e("div",ws,[s[30]||(s[30]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Route Information",-1)),e("div",Ms,[e("div",null,[s[26]||(s[26]=e("strong",null,"Current Path:",-1)),C(" "+v(w.$route.path),1)]),e("div",null,[s[27]||(s[27]=e("strong",null,"Route Name:",-1)),C(" "+v(w.$route.name),1)]),e("div",null,[s[28]||(s[28]=e("strong",null,"Page Title:",-1)),C(" "+v((M=w.$route.meta)==null?void 0:M.title),1)]),e("div",null,[s[29]||(s[29]=e("strong",null,"Page Subtitle:",-1)),C(" "+v((A=w.$route.meta)==null?void 0:A.subtitle),1)])])])])}}}),As=[{path:"/admin-spa",component:de,meta:{requiresAuth:!0,requiresAdmin:!0,title:"Admin Panel"},children:[{path:"",redirect:"/admin-spa/dashboard"},{path:"dashboard",name:"admin-dashboard",component:qt,meta:{title:"Dashboard Overview",subtitle:"Monitor your auction platform performance"}},{path:"test",name:"admin-test",component:Cs,meta:{title:"Component Test",subtitle:"Testing admin layout components"}}]}],ce=ye({history:xe(),routes:As,scrollBehavior(l,t,a){return a||{top:0}}});ce.beforeEach(async(l,t,a)=>{l.meta.title&&(document.title=`${l.meta.title} - Vertigo AMS Admin`);const d=F();if(!d.user)try{await d.initialize()}catch(u){console.error("Failed to initialize auth:",u)}l.meta.requiresAuth&&Ss(),l.meta.requiresAdmin&&($s()||console.log("Allowing access for testing purposes")),a()});function Ss(){const l=F(),t=l.sessionAuth||l.user&&!l.token,a=!!(l.token&&l.user);return!!(t||a||l.isAuthenticated)}function $s(){var a;const l=F();if(l.isAuthenticated||l.user)return!0;const t=l.user;return((a=t==null?void 0:t.roles)==null?void 0:a.some(d=>d.name==="admin"))||!1}const Is={template:`
    <div class="min-h-screen bg-gray-100">
      <router-view />
      <NotificationContainer />
    </div>
  `,setup(){const l=J(),t=F(),a=T();return(async()=>{try{await t.initialize(),a.initialize(),l.success("Admin panel loaded successfully!")}catch(u){console.error("Failed to initialize admin app:",u),l.error("Failed to initialize admin panel")}})(),{}}},x=ge(Is),js=he();x.use(js);x.use(ce);x.component("Button",j);x.component("Input",ae);x.component("Card",H);x.component("Modal",be);x.component("Select",ke);x.component("Loading",_e);x.component("Container",we);x.component("Badge",Me);x.component("Alert",Ce);x.component("Pagination",Ae);x.component("NotificationContainer",Se);x.component("AdminLayout",de);x.component("AdminSidebar",oe);x.component("AdminHeader",ie);x.component("AdminContainer",le);x.component("AdminNavigation",ne);x.component("AdminMenuItem",B);x.component("AdminMobileMenu",re);x.component("MobileMenuItem",R);x.config.errorHandler=(l,t,a)=>{console.error("Admin Vue error:",l,a),J().error("An error occurred in the admin panel")};x.config.warnHandler=(l,t,a)=>{console.warn("Admin Vue warning:",l,a)};x.mount("#admin-app");window.adminApp=x;
