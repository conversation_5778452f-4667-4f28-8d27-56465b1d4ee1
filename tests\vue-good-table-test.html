<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue Good Table Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/vue-good-table-next@0.2.1/dist/vue-good-table.umd.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/vue-good-table-next@0.2.1/dist/vue-good-table-next.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .test-status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>Vue Good Table Test</h1>
            
            <div v-if="testStatus" :class="['test-status', testStatus.type]">
                {{ testStatus.message }}
            </div>
            
            <div v-if="tableReady">
                <h2>Sample Orders Table</h2>
                <vue-good-table
                    :columns="columns"
                    :rows="rows"
                    :pagination-options="paginationOptions"
                    :search-options="searchOptions"
                    :sort-options="sortOptions"
                    styleClass="vgt-table striped"
                >
                    <template #table-row="props">
                        <span v-if="props.column.field === 'customer'">
                            <div style="font-weight: 500;">{{ props.row.customer }}</div>
                            <div style="color: #666; font-size: 0.875rem;">{{ props.row.email }}</div>
                        </span>
                        <span v-else-if="props.column.field === 'status'">
                            <span :style="getStatusStyle(props.row.status)">
                                {{ props.row.status }}
                            </span>
                        </span>
                        <span v-else-if="props.column.field === 'amount'">
                            ${{ props.row.amount.toFixed(2) }}
                        </span>
                        <span v-else>
                            {{ props.formattedRow[props.column.field] }}
                        </span>
                    </template>
                </vue-good-table>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { VueGoodTable } = window.VueGoodTableNext;

        createApp({
            components: {
                VueGoodTable
            },
            data() {
                return {
                    testStatus: null,
                    tableReady: false,
                    columns: [
                        {
                            label: 'Order ID',
                            field: 'order_id',
                            sortable: true,
                            width: '120px'
                        },
                        {
                            label: 'Customer',
                            field: 'customer',
                            sortable: true,
                            width: '200px'
                        },
                        {
                            label: 'Amount',
                            field: 'amount',
                            sortable: true,
                            type: 'number',
                            width: '120px'
                        },
                        {
                            label: 'Status',
                            field: 'status',
                            sortable: true,
                            width: '120px'
                        },
                        {
                            label: 'Date',
                            field: 'created_at',
                            sortable: true,
                            type: 'date',
                            dateInputFormat: 'yyyy-MM-dd',
                            dateOutputFormat: 'MMM dd, yyyy',
                            width: '140px'
                        }
                    ],
                    rows: [
                        {
                            order_id: 'ORD-001',
                            customer: 'John Doe',
                            email: '<EMAIL>',
                            amount: 299.99,
                            status: 'Active',
                            created_at: '2024-01-15'
                        },
                        {
                            order_id: 'ORD-002',
                            customer: 'Jane Smith',
                            email: '<EMAIL>',
                            amount: 149.50,
                            status: 'Completed',
                            created_at: '2024-01-14'
                        },
                        {
                            order_id: 'ORD-003',
                            customer: 'Bob Johnson',
                            email: '<EMAIL>',
                            amount: 75.25,
                            status: 'Cancelled',
                            created_at: '2024-01-13'
                        }
                    ],
                    paginationOptions: {
                        enabled: true,
                        mode: 'records',
                        perPage: 10,
                        position: 'bottom',
                        perPageDropdown: [5, 10, 20],
                        dropdownAllowAll: false
                    },
                    searchOptions: {
                        enabled: true,
                        placeholder: 'Search orders...'
                    },
                    sortOptions: {
                        enabled: true,
                        initialSortBy: { field: 'created_at', type: 'desc' }
                    }
                }
            },
            methods: {
                getStatusStyle(status) {
                    const styles = {
                        'Active': 'background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;',
                        'Completed': 'background: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;',
                        'Cancelled': 'background: #fee2e2; color: #dc2626; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;'
                    };
                    return styles[status] || '';
                }
            },
            mounted() {
                try {
                    // Test if VueGoodTable is available
                    if (VueGoodTable) {
                        this.testStatus = {
                            type: 'success',
                            message: '✅ Vue Good Table loaded successfully!'
                        };
                        this.tableReady = true;
                    } else {
                        throw new Error('VueGoodTable not found');
                    }
                } catch (error) {
                    this.testStatus = {
                        type: 'error',
                        message: '❌ Error loading Vue Good Table: ' + error.message
                    };
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
