<template>
  <nav class="flex-1 flex flex-col p-4 overflow-y-auto mobile-scroll">
    <!-- Search Bar -->
    <div v-if="!adminStore.sidebarCollapsed" class="mb-4 relative">
      <input
        type="text"
        v-model="searchQuery"
        placeholder="Search..."
        class="w-full px-4 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition"
      />
      <svg class="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    </div>

    <!-- Navigation Links -->
    <div class="flex-1 space-y-2">
      <AdminMenuItem
        v-for="item in navigationItems"
        :key="item.key"
        :item="item"
        :collapsed="adminStore.sidebarCollapsed"
      />
    </div>
  </nav>
</template>

<script setup lang="ts">
import { useAdminStore } from '@/stores/admin';
import { useAdminNavigation } from '@/composables/admin/useAdminNavigation';
import AdminMenuItem from './AdminMenuItem.vue';

// Store
const adminStore = useAdminStore();

// Navigation
const { navigationItems, searchQuery } = useAdminNavigation();
</script>

<style scoped>
/* Mobile optimizations */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}
</style>
