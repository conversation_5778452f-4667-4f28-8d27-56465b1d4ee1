declare module 'vue-good-table-next' {
  import { DefineComponent } from 'vue'
  
  export interface VgtColumn {
    label: string
    field: string
    sortable?: boolean
    type?: 'text' | 'number' | 'date' | 'boolean'
    width?: string
    dateInputFormat?: string
    dateOutputFormat?: string
    html?: boolean
    thClass?: string
    tdClass?: string
  }
  
  export interface VgtPaginationOptions {
    enabled?: boolean
    mode?: 'records' | 'pages'
    perPage?: number
    position?: 'top' | 'bottom' | 'both'
    perPageDropdown?: number[]
    dropdownAllowAll?: boolean
    setCurrentPage?: number
    nextLabel?: string
    prevLabel?: string
    rowsPerPageLabel?: string
    ofLabel?: string
    pageLabel?: string
    allLabel?: string
  }
  
  export interface VgtSearchOptions {
    enabled?: boolean
    trigger?: 'enter' | 'keyup'
    skipDiacritics?: boolean
    placeholder?: string
    searchFn?: (row: any, col: VgtColumn, cellValue: any, searchTerm: string) => boolean
  }
  
  export interface VgtSortOptions {
    enabled?: boolean
    initialSortBy?: {
      field: string
      type: 'asc' | 'desc'
    }
    multipleColumns?: boolean
  }
  
  export interface VgtProps {
    columns: VgtColumn[]
    rows: any[]
    paginationOptions?: VgtPaginationOptions
    searchOptions?: VgtSearchOptions
    sortOptions?: VgtSortOptions
    loading?: boolean
    styleClass?: string
    theme?: string
  }
  
  export const VueGoodTable: DefineComponent<VgtProps>
  
  export default VueGoodTable
}
